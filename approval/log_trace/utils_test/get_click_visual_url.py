# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import urllib2

import json
import sys
from datetime import datetime, timedelta

import time
from workflow import Workflow, web, ICON_WEB

reload(sys)  # Python2.5 初始化后会删除 sys.setdefaultencoding 这个方法，我们需要重新载入
sys.setdefaultencoding('utf-8')

wf = Workflow()
log = wf.logger

es_query_template = {
    "from": 0,
    "size": 1,
    "query": {
        "bool": {
            "filter": [
                {
                    "match_phrase": {
                        "traceId": "E-E.ruijie2021.13962-1690967624482"
                    }
                }

            ],
            "should": [],
            "must_not": []
        }
    }
}

url = 'https://oss.foneshare.cn/kibana01/api/console/proxy?path=log-cep-%2A%2F_search&method=GET'


def get_query_es_data(trace_id):
    es_query_template['query']['bool']['filter'][0]['match_phrase']['traceId'] = trace_id
    es_query_json = json.dumps(es_query_template)
    cookie = "sid=Fe26.2**2bde0c5e1fdf778c0249e77dd6caa884b194c292e55f62e52688f55bfa2e2318*kdJXt6Z7SCzroIPWVYcTOA*nUdx_sOhczsGJvH5sunybLW-m20L4ELmp3Z2OtWW75Yv8BM-r0UL0NsXHsUpm8Ckn9YoPdTaGPsEO4h1mBFkNohpQHhZOFKlbY730k8wAuSIWJNCPWma-BwYd2pVaj4Cs5hoodrP8DAg0sTGpKrvU-DEtaReLV6vlG2JvxvZ4vw5OSRwyT3YiS8rdDJUajrKpZm1QbDxC3SRMFuVoKbyTj9nelAoiRRTkxiLi-hoVeDBMeuiX1Sgp4M7V9N0s-bS**5619b5d0f33818b9150cbc144c24f471dcf4b28dcea1a2ed0807804a03ab3616*XYFEJoSX40wzXfKaGi5dJcdko7NiLI2rAu4hXuq-_JM"
    headers = {
        'authority': 'oss.foneshare.cn',
        'accept': 'text/plain, */*; q=0.01',
        'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'content-type': 'application/json',
        'cookie': cookie,
        'kbn-xsrf': 'kibana',
        'origin': 'https://oss.foneshare.cn',
        'referer': 'https://oss.foneshare.cn/kibana01/app/dev_tools',
        'sec-ch-ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
    }

    request = urllib2.Request(url, data=es_query_json, headers=headers)
    response = urllib2.urlopen(request)
    content = response.read()
    return content


def get_click_visual_url(timestamp, trace_id):
    dt = datetime.fromtimestamp(timestamp / 1000.0)
    two_minutes_before = dt - timedelta(minutes=1)
    two_minutes_after = dt + timedelta(minutes=1)
    st = int(time.mktime(two_minutes_before.timetuple()))
    et = int(time.mktime(two_minutes_after.timetuple()))
    return 'https://log.foneshare.cn/query?end=' + str(
        et) + '&index=-1&kw=%20%20traceId%20%3D%20%27' + trace_id + '%27&logState=NaN&page=1&queryType=rawLog&size=100&start=' + str(
        st) + '&tab=custom&tid=104'


def doAction(wf):
    trace_id = wf.args[0]
    json_result = json.loads(get_query_es_data(trace_id))

    stamp = ""
    trace_id = ""

    hits = json_result["hits"]["hits"]
    for hit in hits:
        source = hit["_source"]
        stamp = source["stamp"]
        trace_id = source["traceId"]
        break

    wf.add_item(title='Click Visual', arg=get_click_visual_url(stamp, trace_id), valid=True)
    wf.send_feedback()


if __name__ == '__main__':
    sys.exit(wf.run(doAction))
