# -*- coding: utf-8 -*-
"""
批量更新审批意见脚本 - 无外部依赖版本

功能说明：
1. 同时更新三个地方的意见数据，确保完全同步：
   - MongoDB中的tasks.opinions字段
   - ApprovalTaskObj对象的opinions字段（JSON字符串格式）
   - ApproverOpinionObj对象的opinion字段
2. 确保数据一致性，避免各个存储位置数据不同步的问题

更新流程：
1. 先更新MongoDB中的tasks.opinions
2. 然后更新ApprovalTaskObj.opinions（解析JSON字符串，找到对应opinionId并更新）
3. 最后更新ApproverOpinionObj.opinion
4. 任何一步失败都会终止后续更新，确保数据一致性

使用说明：
1. 修改下面的配置参数
2. 直接运行: python update_opinions_from_csv.py
3. 数据已内嵌在脚本中，无需外部CSV文件
"""
__author__ = 'cuiyx'

import pymongo
import sys
import os
import requests
import json
from bson.objectid import ObjectId

reload(sys)
sys.setdefaultencoding("utf-8")

# 添加根目录到路径，以便导入 engine_route_manager
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(root_dir)

from engine_route_manager import MongoEngineManager

# 配置信息
config = {
    "foneshare": {
        "apibus_url": "http://172.17.4.230:30393/API/v1",
    },
    "ceshi112": {
        "apibus_url": "http://10.112.5.251:16903/API/v1",
    }
}

"""
=== 配置参数 - 直接在这里修改 ===
"""
current_config = "ceshi112"  # 环境配置: "foneshare" 或 "ceshi112"
tenant_id = "71557"          # 租户ID
update = True               # 执行模式: True=实际更新数据库, False=预览模式

print("=" * 60)
print("当前配置:")
print("  环境: {0}".format(current_config))
print("  租户ID: {0}".format(tenant_id))
print("  模式: {0}".format("实际更新" if update else "预览模式"))
print("=" * 60)

# 内嵌的更新数据（从CSV文件转换而来）
# 格式：[{"taskId": "xxx", "opinionId": "xxx", "需要修改为": "xxx"}, ...]
EMBEDDED_UPDATE_DATA = [
    {"taskId": "6560b35748e3ef522246e866", "opinionId": "6560b4ade08ae4469dc72d16", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "6560b5fb4db95f50a572270d", "opinionId": "6560b6d14db95f50a572272a", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "6560b5fb4db95f50a572270d", "opinionId": "6560b7e61be21c3d5067a37c", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "6572e67457b88217a7b2ec84", "opinionId": "6572e6e8e181ae33b4afb76f", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "6582c651dd35a3025a2075fe", "opinionId": "6582c8452012b320c39d7b61", "需要修改为": "市场担保，同意"},
    {"taskId": "6582c79aca2f0e741219fc6b", "opinionId": "6582c85aca2f0e741219fceb", "需要修改为": "市场担保，同意"},
    {"taskId": "65856250bf4a8a184a883bbc", "opinionId": "6585927dca2f0e74121b9872", "需要修改为": "经销客户，存在逾期15.06万，逾期11天，未通过资信评估不可赊销出货\\n基于客户存在足够未使用的返点余额，考虑风险情况，特批出货\\n出货订单金额28.8万，赊销50%（14.4万）"},
    {"taskId": "658564b443e0f96d1fa979f1", "opinionId": "6585937c2012b320c39f0f19", "需要修改为": "经销客户，存在逾期15.06万，逾期11天，未通过资信评估不可赊销出货\\n基于客户存在足够未使用的返点余额，考虑风险情况，特批出货\\n出货订单金额1.5万，赊销90%（1.34万）"},
    {"taskId": "6585665cea98ef2860a0a06e", "opinionId": "658595692012b320c39f0f88", "需要修改为": "经销客户，存在逾期15.06万，逾期11天，未通过资信评估不可赊销出货\\n基于客户存在足够未使用的返点余额，考虑风险情况，特批出货\\n出货订单金额合计65.7万，赊销90%（59.1万）"},
    {"taskId": "6588f33f57b88217a7fe9b4f", "opinionId": "658908b2cfe0605afc08481c", "需要修改为": "1、经销客户，存在逾期8万，实际用协议款抵扣，实际无逾期\\n2、客户本身+同一控制人另一主体盐城星月，总授信合计350万；\\n3、超授信出货，经体系负责人，销售分管领导，财务总监特批出货，但条件如下\\nA、每月至少付款70万，剩余按账期到期必须清完，要星月提供承诺函；\\nB、给南京星月和盐城星月分别实缴500万，2024年2月底前；\\nC、订单所在区域负责人给星月按应收制度要求对《1、每月回款70万，累计580万元-即超授信部分》做担保。如未按期回款，按担保金额的1%从工资扣除；\\n4、本流程出货订单金额102.1万，赊销90%（91.85万）"},
    {"taskId": "6588f59a57b88217a7feaae3", "opinionId": "658909b1ca2f0e74121c7fef", "需要修改为": "1、经销客户，存在逾期8万，实际用协议款抵扣，实际无逾期\\n2、客户本身+同一控制人另一主体盐城星月，总授信合计350万，\\n3、超授信出货，经体系负责人，销售分管领导，财务总监特批出货，但条件如下\\nA、每月至少付款70万，剩余按账期到期必须清完，要星月提供承诺函；\\nB、给南京星月和盐城星月分别实缴500万，2024年2月底前；\\nC、订单所在区域负责人给星月按应收制度要求对《1、每月回款70万，累计580万元-即超授信部分》做担保。如未按期回款，按担保金额的1%从工资扣除；\\n4、本流程出货订单金额154.5万，赊销90%（139万）"},
    {"taskId": "6588f606ea98ef2860a773f2", "opinionId": "65890a97dd35a3025a22f789", "需要修改为": "1、经销客户，存在逾期8万，实际用协议款抵扣，实际无逾期\\n2、客户本身+同一控制人另一主体盐城星月，总授信合计350万，\\n3、超授信出货，经体系负责人，销售分管领导，财务总监特批出货，但条件如下\\nA、每月至少付款70万，剩余按账期到期必须清完，要星月提供承诺函；\\nB、给南京星月和盐城星月分别实缴500万，2024年2月底前；\\nC、订单所在区域负责人给星月按应收制度要求对《1、每月回款70万，累计580万元-即超授信部分》做担保。如未按期回款，按担保金额的1%从工资扣除；\\n\\n4、本流程出货订单金额360.1万，赊销57%（205.2万）"},
    {"taskId": "658b93f157b88217a708f4ca", "opinionId": "658bb90a2012b320c3a1b440", "需要修改为": "逾期40.37万（1-17天），鉴于客户已办理房产抵押，特批出货。"},
    {"taskId": "658b93f157b88217a708f4ca", "opinionId": "658bbb05cfe0605afc0a0c06", "需要修改为": "逾期40.37万（1-17天），鉴于客户已办理房产抵押，特批出货。"},
    {"taskId": "658b9461c099792a2bae4476", "opinionId": "658bb93b2012b320c3a1b467", "需要修改为": "逾期40.37万（1-17天），鉴于客户已办理房产抵押，特批出货。"},
    {"taskId": "658b9461c099792a2bae4476", "opinionId": "658bbb10ca2f0e74121e39f3", "需要修改为": "逾期40.37万，鉴于客户已办理房产抵押，特批出货。"},
    {"taskId": "658b985fbf4a8a184aa91c72", "opinionId": "658bb95fcfe0605afc0a09da", "需要修改为": "逾期40.37万（1-17天），鉴于客户已办理房产抵押，特批出货。"},
    {"taskId": "658b985fbf4a8a184aa91c72", "opinionId": "658bbb1bdd35a3025a24b41d", "需要修改为": "逾期40.37万（1-17天），鉴于客户已办理房产抵押，特批出货。"},
    {"taskId": "658bd8caea98ef2860b1c09b", "opinionId": "658be711dd35a3025a24fbc0", "需要修改为": "1、经销客户，存在逾期8万，实际用协议款抵扣，实际无逾期\\n2、客户本身+同一控制人另一主体盐城星月，总授信合计350万，3、超授信出货，经体系负责人，销售分管领导，财务总监特批出货，但条件如下\\nA、每月至少付款70万，剩余按账期到期必须清完，要星月提供承诺函；\\nB、给南京星月和盐城星月分别实缴500万，2024年2月底前；\\nC、订单所在区域负责人给星月按应收制度要求对《1、每月回款70万，累计580万元-即超授信部分》做担保。如未按期回款，按担保金额的1%从工资扣除；\\n4、本流程出货订单金额200.1万，赊销90%（180.1万）"},
    {"taskId": "658d4529ea98ef2860b7f663", "opinionId": "658d4792ca2f0e74121f6d7b", "需要修改为": "经销商逾期160.5万，其中41.5万逾期9-10天，51万逾期35天，68万逾期46-52天；回款计划：新增41.5万客户承诺1月18日付清；余下部分提供区总担保，12月31日需还款64万，1月30日还款64万。小单凭区总担保出货。"},
    {"taskId": "658d4529ea98ef2860b7f663", "opinionId": "658d4fb62012b320c3a2ef6d", "需要修改为": "经销商逾期160.5万，其中41.5万逾期9-10天，51万逾期35天，68万逾期46-52天；回款计划：新增41.5万客户承诺1月18日付清；余下部分提供区总担保，12月31日需还款64万，1月30日还款64万。小单凭区总担保出货。"},
    {"taskId": "659f931dbbbd0e35ee556ad5", "opinionId": "659f996d9a3acf36d1c26b75", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "659f931dbbbd0e35ee556ad5", "opinionId": "65a091491e57f868378a31f7", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "65a61a65938b0143e08b3526", "opinionId": "65a61f24f618c53a4fce01bf", "需要修改为": "本次出货为之前订单剩下的备件包，鉴于此，本单OK。"},
    {"taskId": "65a6a8a0bbbd0e35ee6a5f2a", "opinionId": "65a72403f618c53a4fce657c", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "65a72fccf618c53a4fce703c", "opinionId": "65a73c491e57f868378cea0f", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "65af2e250bbea4713ab32698", "opinionId": "65af33dbb9dfdc61be8eb448", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "65afa64de0e076416ebb7e4e", "opinionId": "65afa764ff8fa271b93e325c", "需要修改为": "同意"},
    {"taskId": "65b764e1444fa236a98b8f33", "opinionId": "65b76ab9bd65b718d6ab758c", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "65d016123b42ef3b40d18e7f", "opinionId": "65d017b413e8a66b68f672fc", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "65d2c19a13e8a66b68f6fc08", "opinionId": "65d2c39d3b42ef3b40d21b42", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "65df0cdf453dd919c659fb4f", "opinionId": "65df16caa3efac16b7e27b98", "需要修改为": "鉴于客户资质，本次建议同意"},
    {"taskId": "65df0cdf453dd919c659fb4f", "opinionId": "65dfd65f13e8a66b68fb8e45", "需要修改为": "同意出货，逾期请在期限内闭环"},
    {"taskId": "65e0362ea252771cad3e385c", "opinionId": "65e036e9a3efac16b7e2eff8", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "65e0362ea252771cad3e385c", "opinionId": "65e038d1a252771cad3e3b90", "需要修改为": "业务区跟进回款闭环"},
    {"taskId": "65e7dddea836ed01b58a41da", "opinionId": "65e81d0b20d67d1fbc293285", "需要修改为": "基于担保，同意逾期特批出货。"},
    {"taskId": "65e7de49ac90e67c0c2b7047", "opinionId": "65e81d2c64c5cf47e66098f6", "需要修改为": "基于担保，同意逾期特批出货。"},
    {"taskId": "6624d22f48e5ce4f7331e2d3", "opinionId": "6625b9bd96a88b40f792a833", "需要修改为": "申请特批出货，请领导审批"},
    {"taskId": "6629babdd27ebe0e9f08d196", "opinionId": "6629bc64edfe587955d8b61c", "需要修改为": "同意"},
    {"taskId": "665d91e10a6bc04f1a8a7054", "opinionId": "665eb045f86e7246d7e474c8", "需要修改为": "1、采云团购（深圳）有限公司：客户信用等级为B级，授信额度为1200万元，目前额度占用为947万，2024/5/30：3/12收到纸质验收单，请款中。 担保6/30回款。"},
    {"taskId": "665d93143bbdf314ef2e0412", "opinionId": "665eb02da1a6a13e0a7118a3", "需要修改为": "1、采云团购（深圳）有限公司：客户信用等级为B级，授信额度为1200万元，目前额度占用为947万，2024/5/30：3/12收到纸质验收单，请款中。 担保6/30回款。\\n2、方特总情况：目前合作的有以下客户，总额度为2000万元，额度占用为1549万元，存在389万元逾期款。\\n3、此订单在总额度管控范围内。\\n综上，请领导审批。"},
    {"taskId": "6667ef6a49a7250a98c2ee30", "opinionId": "666814d002fdd840997ab325", "需要修改为": "1、采云团购（深圳）有限公司：客户信用等级为B级，授信额度为1200万元，目前额度占用为947万，2024/5/30：3/12收到纸质验收单，请款中。 担保7/30回款。\\n2、方特总情况：目前合作的有以下客户，总额度为2000万元，额度占用为1549万元，存在389万元逾期款。\\n3、此订单在总额度管控范围内。\\n综上，请领导审批。"},
    {"taskId": "6760df56d5f506067c182cd6", "opinionId": "6760fc47d5f506067c18648e", "需要修改为": "同意"},
    {"taskId": "676273afd5f506067c19eff6", "opinionId": "676277bb26eecc6a41efddf3", "需要修改为": "同意"},
    {"taskId": "67f40149b6b4fe38850b15b6", "opinionId": "67f49e63b6b4fe38850b70b2", "需要修改为": "同意"},
    {"taskId": "686619cb3829cb05bfdcb85b", "opinionId": "68661c1c66b7622a5fb6bc3d", "需要修改为": "同意"},
    {"taskId": "686625003829cb05bfdccd1f", "opinionId": "68663fa40b6fcb5748cab86b", "需要修改为": "同意"},
    {"taskId": "6589307d43e0f96d1fb507d4", "opinionId": "65893612cfe0605afc088031", "需要修改为": "同意出货"},
    {"taskId": "66cd8cfd61c5a828eed3e0bf", "opinionId": "66cd9a7fa732cc19cc0d6771", "需要修改为": "信保已经批复\\n业务区做好把控\\n同意"},
    {"taskId": "64c472f2b8b220608ffa778d", "opinionId": "64c493914365727dfb8cd576", "需要修改为": "请修改"},
    {"taskId": "64c472e2b8b220608ffa770c", "opinionId": "64c4939da5de4d0a7f109e1c", "需要修改为": "请修改"},
    {"taskId": "67ca9799ee2f9563a710260a", "opinionId": "67ca991e12943329b0102136", "需要修改为": "该客户在公司业务区内有逾期，在服务内部无逾期，系统自动上升CSDT决策；"},
    {"taskId": "6877bd7ede42893502afe101", "opinionId": "68787bab939490353927ff98", "需要修改为": "同意"}
]


def get_embedded_data():
    """获取内嵌的更新数据"""
    # 处理换行符：将\\n转换为实际的换行符
    processed_data = []
    for item in EMBEDDED_UPDATE_DATA:
        processed_item = item.copy()
        processed_item["需要修改为"] = processed_item["需要修改为"].replace("\\n", "\n")
        processed_data.append(processed_item)
    return processed_data


def get_approval_task_obj(tenant_id, task_id):
    """
    获取ApprovalTaskObj对象数据
    """
    try:
        url = config[current_config]["apibus_url"] + "/inner/rest/object_data/ApprovalTaskObj/" + task_id

        querystring = {
            "includeInvalid": "false",
            "includeDeleted": "false",
            "includeDescribe": "false"
        }

        headers = {
            'content-type': "application/json",
            'x-fs-userinfo': "-10000",
            'x-fs-ei': str(tenant_id),
            'x-tenant-id': str(tenant_id),
            'x-user-id': "-10000"
        }

        response = requests.request("GET", url, headers=headers, params=querystring)
        if response.status_code == 200:
            response_data = json.loads(response.content)
            return True, response_data.get("data", {}).get("object_data", {})
        else:
            return False, "查询失败: {0}".format(response.status_code)

    except Exception as e:
        return False, "查询异常: {0}".format(str(e))


def update_approval_task_obj(tenant_id, task_id, opinion_id, new_opinion):
    """
    更新ApprovalTaskObj对象中的opinions字段
    opinions是一个JSON字符串，包含意见列表
    """
    try:
        # 1. 先获取当前的ApprovalTaskObj数据
        success, task_data = get_approval_task_obj(tenant_id, task_id)
        if not success:
            return False, "获取ApprovalTaskObj失败: {0}".format(task_data)

        # 2. 解析opinions字段
        opinions_str = task_data.get("opinions", "[]")
        if not opinions_str:
            opinions_str = "[]"

        try:
            opinions_list = json.loads(opinions_str)
        except:
            opinions_list = []

        # 3. 查找并更新对应的意见
        updated = False
        for opinion in opinions_list:
            if opinion.get("id") == opinion_id:
                opinion["opinion"] = new_opinion
                updated = True
                break

        if not updated:
            return False, "未找到对应的opinionId: {0}".format(opinion_id)

        # 4. 将更新后的列表转回JSON字符串
        updated_opinions_str = json.dumps(opinions_list, ensure_ascii=False)

        # 5. 更新ApprovalTaskObj
        url = config[current_config]["apibus_url"] + "/inner/rest/object_data/ApprovalTaskObj/" + task_id

        querystring = {
            "includeDescribe": "false",
            "incrementalUpdate": "true",
            "triggerFlow": "false",
            "applyValidationRule": "false",
            "applyDataPrivilegeCheck": "false"
        }

        headers = {
            'x-fs-userinfo': "-10000",
            'x-fs-ei': str(tenant_id),
            'content-type': "application/json"
        }

        update_data = {"opinions": updated_opinions_str}

        response = requests.request("PUT", url, headers=headers, params=querystring, data=json.dumps(update_data))

        if response.status_code == 200:
            return True, "更新成功"
        else:
            response_data = json.loads(response.content)
            return False, response_data.get("message", "更新失败")

    except Exception as e:
        return False, "更新异常: {0}".format(str(e))


def update_approver_opinion_obj(tenant_id, opinion_id, new_opinion):
    """
    更新ApproverOpinionObj对象中的意见内容
    """
    try:
        url = config[current_config]["apibus_url"] + "/inner/rest/object_data/ApproverOpinionObj/" + opinion_id

        querystring = {
            "includeDescribe": "false",
            "incrementalUpdate": "true",
            "triggerFlow": "false",
            "applyValidationRule": "false",
            "applyDataPrivilegeCheck": "false"
        }

        headers = {
            'x-fs-userinfo': "-10000",
            'x-fs-ei': str(tenant_id),
            'content-type': "application/json"
        }

        update_data = {"opinion": new_opinion}

        response = requests.request("PUT", url, headers=headers, params=querystring, data=json.dumps(update_data))
        response_data = json.loads(response.content)

        if response.status_code == 200:
            return True, "更新成功"
        else:
            return False, response_data.get("message", "更新失败")

    except Exception as e:
        return False, "更新异常: {0}".format(str(e))


def update_opinions_batch(engine_tdb, tenant_id, update_data):
    """批量更新意见数据"""

    success_count = 0
    error_count = 0

    for i, data in enumerate(update_data, 1):
        task_id = data.get('taskId', '').strip()
        opinion_id = data.get('opinionId', '').strip()
        new_opinion = data.get('需要修改为', '').strip()

        if not all([task_id, opinion_id, new_opinion]):
            print("跳过无效数据: {0}".format(data))
            error_count += 1
            continue

        try:
            # 构建更新操作
            filter_query = {
                "tenantId": tenant_id,
                "_id": ObjectId(task_id),
                "opinions.id": opinion_id
            }

            update_query = {
                "$set": {
                    "opinions.$.opinion": new_opinion
                }
            }

            if update:
                # 1. 实际执行MongoDB更新
                result = engine_tdb.tasks.update_one(filter_query, update_query)
                if result.modified_count > 0:
                    print("[{0}/{1}] MongoDB更新成功 - taskId:{2}, opinionId:{3}".format(
                        i, len(update_data), task_id, opinion_id))

                    # 2. 同步更新ApprovalTaskObj的opinions字段
                    task_success, task_msg = update_approval_task_obj(tenant_id, task_id, opinion_id, new_opinion)
                    if task_success:
                        print("[{0}/{1}] ApprovalTaskObj更新成功 - taskId:{2}, opinionId:{3}".format(
                            i, len(update_data), task_id, opinion_id))
                    else:
                        print("[{0}/{1}] ApprovalTaskObj更新失败 - taskId:{2}, opinionId:{3}, 错误:{4}".format(
                            i, len(update_data), task_id, opinion_id, task_msg))
                        error_count += 1
                        continue  # ApprovalTaskObj更新失败，跳过ApproverOpinionObj更新

                    # 3. 同步更新ApproverOpinionObj
                    approver_success, approver_msg = update_approver_opinion_obj(tenant_id, opinion_id, new_opinion)
                    if approver_success:
                        success_count += 1
                        print("[{0}/{1}] ApproverOpinionObj更新成功 - opinionId:{2}".format(
                            i, len(update_data), opinion_id))
                        print("[{0}/{1}] ✓ 全部更新完成 - MongoDB + ApprovalTaskObj + ApproverOpinionObj".format(
                            i, len(update_data)))
                    else:
                        print("[{0}/{1}] ApproverOpinionObj更新失败 - opinionId:{2}, 错误:{3}".format(
                            i, len(update_data), opinion_id, approver_msg))
                        error_count += 1
                else:
                    print("[{0}/{1}] MongoDB更新失败 - taskId:{2}, opinionId:{3} (未找到匹配记录)".format(
                        i, len(update_data), task_id, opinion_id))
                    error_count += 1
            else:
                # 预览模式
                print("[{0}/{1}] 预览 - taskId:{2}, opinionId:{3}".format(
                    i, len(update_data), task_id, opinion_id))
                print("    新意见内容: {0}".format(new_opinion))
                print("    将同步更新:")
                print("      1. MongoDB tasks.opinions")
                print("      2. ApprovalTaskObj.opinions (JSON字符串)")
                print("      3. ApproverOpinionObj.opinion")
                success_count += 1

        except Exception as e:
            print("[{0}/{1}] 处理失败 - taskId:{2}, opinionId:{3}, 错误:{4}".format(
                i, len(update_data), task_id, opinion_id, e))
            error_count += 1

    return success_count, error_count


def main():
    """主函数"""
    print("开始批量更新意见数据...")
    print("配置环境: {0}".format(current_config))
    print("租户ID: {0}".format(tenant_id))
    print("数据来源: 内嵌数据（无需外部CSV文件）")
    print("运行模式: {0}".format("实际更新" if update else "预览模式"))
    print("更新范围:")
    print("  1. MongoDB tasks.opinions")
    print("  2. ApprovalTaskObj.opinions (JSON字符串)")
    print("  3. ApproverOpinionObj.opinion")
    print("-" * 50)

    # 获取内嵌数据
    update_data = get_embedded_data()
    if not update_data:
        print("没有找到需要更新的数据")
        return

    print("读取到 {0} 条待更新数据".format(len(update_data)))

    # 连接MongoDB（只有在执行模式下才需要连接）
    engine_tdb = None
    if update:
        try:
            manager = MongoEngineManager(current_config)
            engine_tdb = manager.get_engine_db(tenant_id)
            print("MongoDB连接成功")
        except Exception as e:
            print("MongoDB连接失败: {0}".format(e))
            return
    else:
        print("预览模式：跳过MongoDB连接")

    # 执行批量更新
    success_count, error_count = update_opinions_batch(engine_tdb, tenant_id, update_data)

    print("-" * 50)
    print("处理完成统计:")
    print("成功处理: {0} 条".format(success_count))
    print("失败记录: {0} 条".format(error_count))
    print("总计处理: {0} 条".format(len(update_data)))

    if not update:
        print("\n注意：这是预览模式，没有实际更新数据库。")
        print("如需实际执行更新，请将脚本顶部的 update = True。")


if __name__ == '__main__':
    main()
