# -*- coding: utf-8 -*-
__author__ = 'cuiyx'
from datetime import datetime
from dateutil.relativedelta import relativedelta


def main():
    # 输入的日期字符串
    input_date_str = "2024-12-31"
    # 将输入的日期字符串转换为日期对象
    input_date = datetime.strptime(input_date_str, "%Y-%m-%d")
    # 计算后一天的日期
    next_day = input_date + relativedelta(days=1)
    # 输出结果
    print(next_day.strftime("%Y-%m-%d"))


if __name__ == "__main__":
    main()