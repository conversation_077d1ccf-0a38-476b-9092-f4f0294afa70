# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import pymongo
import pandas as pd
import sys
import os
from bson.objectid import ObjectId

reload(sys)
sys.setdefaultencoding("utf-8")

# 添加根目录到路径，以便导入 engine_route_manager
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(root_dir)

from engine_route_manager import MongoEngineManager

# 配置信息
config = {
    "foneshare": {
        "apibus_url": "http://172.17.4.230:30393/API/v1",
    },
    "ceshi112": {
        "apibus_url": "http://10.112.5.251:16903/API/v1",
    }
}

# 默认配置
DEFAULT_CONFIG = "ceshi112"
DEFAULT_TENANT_ID = "71557"

update = False


def read_csv_data(csv_file):
    """读取CSV文件中的更新数据"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
        return df.to_dict('records')
    except Exception as e:
        print("读取CSV文件失败: {}".format(e))
        return []


def update_opinions_batch(engine_tdb, tenant_id, update_data):
    """批量更新意见数据"""

    success_count = 0
    error_count = 0

    for data in update_data:
        task_id = data.get('taskId', '').strip()
        opinion_id = data.get('opinionId', '').strip()
        new_opinion = data.get(u'需要修改为', '').strip()

        if not all([task_id, opinion_id, new_opinion]):
            print("跳过无效数据: {}".format(data))
            error_count += 1
            continue

        try:
            # 构建更新操作
            filter_query = {
                "tenantId": tenant_id,
                "_id": ObjectId(task_id),
                "opinions.id": opinion_id
            }

            update_query = {
                "$set": {
                    "opinions.$.opinion": new_opinion
                }
            }
            if update:
                result = engine_tdb.tasks.update_one(filter_query, update_query)
                success_count = result.modified_count
                print("批量更新完成: 成功更新 {} 条记录".format(success_count))
            else:
                print("taskId:{}, opinionId:{}, 意见:{}".format(task_id, opinion_id, new_opinion))

        except Exception as e:
            print("处理数据失败 taskId:{}, opinionId:{}, 错误:{}".format(task_id, opinion_id, e))
            error_count += 1

    return success_count, error_count


def main():
    """主函数"""
    # 配置参数
    current_config = DEFAULT_CONFIG
    tenant_id = DEFAULT_TENANT_ID
    csv_file = 'extracted_three_fields.csv'

    print("开始批量更新意见数据...")
    print("配置环境: {}".format(current_config))
    print("租户ID: {}".format(tenant_id))
    print("数据文件: {}".format(csv_file))
    print("-" * 50)

    # 读取CSV数据
    update_data = read_csv_data(csv_file)
    if not update_data:
        print("没有找到需要更新的数据")
        return

    print("读取到 {} 条待更新数据".format(len(update_data)))

    # 连接MongoDB
    try:
        manager = MongoEngineManager(current_config)
        engine_tdb = manager.get_engine_db(tenant_id)
        print("MongoDB连接成功")
    except Exception as e:
        print("MongoDB连接失败: {}".format(e))
        return

    # 执行批量更新
    success_count, error_count = update_opinions_batch(engine_tdb, tenant_id, update_data)

    print("-" * 50)
    print("更新完成统计:")
    print("成功更新: {} 条".format(success_count))
    print("失败记录: {} 条".format(error_count))
    print("总计处理: {} 条".format(len(update_data)))


if __name__ == '__main__':
    main()
