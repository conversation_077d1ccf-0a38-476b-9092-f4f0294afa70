# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

from datetime import datetime, timedelta


if __name__ == '__main__':
    # date_str = '0229'
    # current_date_obj = datetime.strptime(date_str, '%m%d')
    # print 1

    date_str = '0229'
    current_year = datetime.now().year  # 获取当前年份
    date_str_with_year = "{}{}".format(current_year, date_str)  # 将当前年份与日期字符串组合
    current_date_obj = datetime.strptime(date_str_with_year, '%Y%m%d')
    print(current_date_obj)