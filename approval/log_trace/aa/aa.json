{
  "code": 0,
  "msg": "succ",
  "data": {
    "limited": 500,
    "keys": [
      {
        "id": 5405,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "app",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5406,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "level",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5403,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "logger",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5410,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "msg",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5402,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "pod",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5407,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "profile",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5408,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "thread",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5411,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "token",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5409,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "traceId",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      },
      {
        "id": 5404,
        "ctime": 1734499210,
        "utime": 1734499210,
        "dtime": 0,
        "tid": 278,
        "field": "userId",
        "rootName": "",
        "typ": 0,
        "hashTyp": 0,
        "alias": "",
        "kind": 0
      }
    ],
    "showKeys": null,
    "count": 0,
    "terms": [],
    "hiddenFields": null,
    "defaultFields": [
      "app",
      "level",
      "logger",
      "msg",
      "pod",
      "profile",
      "thread",
      "token",
      "traceId",
      "userId"
    ],
    "logs": [],
    "query": "SELECT * FROM `logger`.`app_log_dist` WHERE _time_second_ \u003e= toDateTime64(1751443809, 3) AND _time_second_ \u003c toDateTime64(1751443929, 3) AND (traceId = 'E-E.autobio.1427-17710712' and level = 'ERROR') ORDER BY _time_second_ DESC LIMIT 500 OFFSET 0",
    "cost": 2128,
    "where": "traceId = 'E-E.autobio.1427-17710712' and level = 'ERROR'",
    "isTrace": 0
  }
}⏎