# -*- coding: utf-8 -*-
"""
测试ApprovalTaskObj的opinions字段更新逻辑
"""
__author__ = 'cuiyx'

import json

def test_opinions_json_parsing():
    """
    测试ApprovalTaskObj的opinions字段解析和更新逻辑
    """
    print("测试ApprovalTaskObj的opinions字段更新")
    print("=" * 50)
    
    # 模拟ApprovalTaskObj的opinions字段内容
    test_opinions_str = '[{"reply_user":["1002"],"action_type":"agree","reply_time":1727487932587,"id":"66f75fbcdeb560342dbd6406","opinion":"同意。"},{"reply_user":["1003"],"action_type":"reject","reply_time":1727487945123,"id":"66f75fbcdeb560342dbd6407","opinion":"不同意，需要补充材料。"}]'
    
    print("原始opinions字符串:")
    print(test_opinions_str)
    print()
    
    # 测试解析
    try:
        opinions_list = json.loads(test_opinions_str)
        print("解析成功，包含 {0} 条意见:".format(len(opinions_list)))
        for i, opinion in enumerate(opinions_list, 1):
            print("  {0}. ID: {1}, 意见: {2}".format(i, opinion.get("id"), opinion.get("opinion")))
        print()
    except Exception as e:
        print("解析失败: {0}".format(e))
        return False
    
    # 测试更新逻辑
    target_opinion_id = "66f75fbcdeb560342dbd6406"
    new_opinion = "业务区跟进回款闭环"
    
    print("测试更新逻辑:")
    print("目标opinionId: {0}".format(target_opinion_id))
    print("新意见内容: {0}".format(new_opinion))
    print()
    
    # 查找并更新
    updated = False
    for opinion in opinions_list:
        if opinion.get("id") == target_opinion_id:
            old_opinion = opinion.get("opinion")
            opinion["opinion"] = new_opinion
            updated = True
            print("✓ 找到并更新意见:")
            print("  原意见: {0}".format(old_opinion))
            print("  新意见: {0}".format(new_opinion))
            break
    
    if not updated:
        print("✗ 未找到对应的opinionId")
        return False
    
    # 转回JSON字符串
    try:
        updated_opinions_str = json.dumps(opinions_list, ensure_ascii=False)
        print("\n更新后的opinions字符串:")
        print(updated_opinions_str)
        print()
        
        # 验证更新结果
        verify_list = json.loads(updated_opinions_str)
        for opinion in verify_list:
            if opinion.get("id") == target_opinion_id:
                if opinion.get("opinion") == new_opinion:
                    print("✓ 验证成功：意见已正确更新")
                else:
                    print("✗ 验证失败：意见更新不正确")
                    return False
                break
        
    except Exception as e:
        print("转换JSON失败: {0}".format(e))
        return False
    
    return True

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "空字符串",
            "opinions": "",
            "expected": "[]"
        },
        {
            "name": "空数组",
            "opinions": "[]",
            "expected": "[]"
        },
        {
            "name": "无效JSON",
            "opinions": "[invalid json}",
            "expected": "[]"
        },
        {
            "name": "单个意见",
            "opinions": '[{"id":"test123","opinion":"原意见"}]',
            "expected": "有效"
        }
    ]
    
    for case in test_cases:
        print("测试用例: {0}".format(case["name"]))
        print("输入: {0}".format(case["opinions"]))
        
        opinions_str = case["opinions"]
        if not opinions_str:
            opinions_str = "[]"
            
        try:
            opinions_list = json.loads(opinions_str)
            print("解析结果: 成功，包含 {0} 条记录".format(len(opinions_list)))
        except:
            opinions_list = []
            print("解析结果: 失败，使用空数组")
        
        print("处理结果: ✓ 正常")
        print()
    
    return True

def test_update_workflow():
    """
    测试完整的更新工作流程
    """
    print("=" * 50)
    print("完整更新工作流程测试")
    print("=" * 50)
    
    workflow_steps = [
        "1. 获取ApprovalTaskObj对象数据",
        "2. 解析opinions字段（JSON字符串）",
        "3. 根据opinionId查找对应的意见记录",
        "4. 更新意见内容",
        "5. 将更新后的列表转回JSON字符串",
        "6. 调用API更新ApprovalTaskObj.opinions字段"
    ]
    
    print("更新工作流程:")
    for step in workflow_steps:
        print("  " + step)
    
    print("\nAPI调用信息:")
    print("- GET: /inner/rest/object_data/ApprovalTaskObj/{taskId}")
    print("- PUT: /inner/rest/object_data/ApprovalTaskObj/{taskId}")
    print("- 参数: incrementalUpdate=true, triggerFlow=false")
    print("- 请求头: x-fs-userinfo=-10000, x-fs-ei={tenant_id}")
    
    print("\n错误处理:")
    print("- 获取ApprovalTaskObj失败 → 跳过此记录")
    print("- opinions字段解析失败 → 使用空数组")
    print("- 未找到对应opinionId → 报错并跳过")
    print("- API更新失败 → 记录错误，不继续后续更新")
    
    return True

if __name__ == "__main__":
    print("ApprovalTaskObj更新功能测试")
    print("=" * 60)
    
    test1_passed = test_opinions_json_parsing()
    test2_passed = test_edge_cases()
    test3_passed = test_update_workflow()
    
    print("=" * 60)
    if test1_passed and test2_passed and test3_passed:
        print("✓ 所有测试通过！ApprovalTaskObj更新逻辑正确。")
        print("\n关键点:")
        print("1. opinions字段是JSON字符串，需要解析为列表")
        print("2. 根据id字段匹配opinionId")
        print("3. 更新opinion字段内容")
        print("4. 转回JSON字符串并更新到ApprovalTaskObj")
        print("5. 确保中文字符正确处理（ensure_ascii=False）")
    else:
        print("✗ 部分测试失败，请检查逻辑。")
