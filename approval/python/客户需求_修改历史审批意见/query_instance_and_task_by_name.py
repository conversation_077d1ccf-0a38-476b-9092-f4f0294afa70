# -*- coding: utf-8 -*-
#!/usr/bin/env python
# encoding=utf-8
import json
import datetime
import sys
import requests
import xlwt
import pymongo
from bson import ObjectId

reload(sys)
sys.setdefaultencoding("utf-8")

tenant_id = "71557"

env = "ceshi112"


config = {
    "foneshare": {
        "udobj_url": "http://172.17.4.230:30393/API"

    },
    "ceshi112": {
        "udobj_url": "http://10.112.5.251:16903/API"
    }
}


def get_data_by_template(entityId, names):
    url = config[env]["udobj_url"] + "/v1/inner/rest/object_data/" + entityId + "/queryBySearchTemplate"
    payload = {
        "limit": 500,
        "offset": 0,
        "filters": [
            {
                "field_name": "tenant_id",
                "field_values": [
                    tenant_id
                ],
                "operator": "EQ",
                "connector": "AND",
                "fieldNum": 0,
                "isObjectReference": False,
                "isIndex": False
            },
            {
                "field_name": "name",
                "field_values": names,
                "operator": "IN",
                "connector": "AND",
                "fieldNum": 0,
                "isObjectReference": False,
                "isIndex": False
            }
        ]
    }

    headers = {
        'content-type': "application/json",
        'x-fs-ei': tenant_id,
        'x-fs-userinfo': "-10000"
    }
    response = requests.request("POST", url, data=json.dumps(payload), headers=headers)
    rst_text = response.text
    rst = json.loads(rst_text)
    try:
        code = get_attr(rst, "code")
        if code == 320002404 or code == 500:
            return {"total": 0, "data": []}
        return rst["data"]["queryResult"]["data"]
    except Exception, e:
        print rst
        raise Exception("Invalid level!", rst["message"])


def get_attr(obj, key):
    data = ""
    if key in obj:
        data = obj[key]
    return data


if __name__ == '__main__':
    datas = get_data_by_template("object_UPfNh__c",
                                 ["自行车-2025-06-251530", "自行车-2025-05-221527", "自行车-2025-01-081521"])
    for data in datas:
        print data.get("_id")
