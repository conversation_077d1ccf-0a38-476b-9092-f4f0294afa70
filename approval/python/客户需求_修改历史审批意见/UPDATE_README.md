# 批量更新审批意见脚本使用说明

## 概述

`update_opinions_from_csv.py` 是一个用于批量更新审批意见的脚本，已优化为无需外部依赖，数据直接内嵌在脚本中。

## 主要特性

- ✅ **无外部依赖**：不需要pandas库，不需要外部CSV文件
- ✅ **数据内嵌**：所有更新数据直接嵌入在Python文件中
- ✅ **预览模式**：支持预览将要更新的内容，无需实际执行
- ✅ **安全执行**：必须明确指定执行模式才会实际更新数据库
- ✅ **跳板机友好**：适合在无外网连接的跳板机上运行

## 命令行参数

| 参数 | 简写 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--config` | `-c` | 否 | ceshi112 | 配置环境 (foneshare/ceshi112) |
| `--tenant-id` | `-t` | 否 | 71557 | 租户ID |
| `--preview` | `-p` | 是* | 无 | 预览模式：只显示将要更新的内容 |
| `--execute` | `-e` | 是* | 无 | 执行模式：实际执行数据库更新 |

*注：`--preview` 和 `--execute` 必须选择其一

## 使用示例

### 1. 预览模式（推荐先运行）
查看将要更新的内容，不实际修改数据库：
```bash
python update_opinions_from_csv.py --preview
```

### 2. 指定环境的预览
```bash
python update_opinions_from_csv.py --preview --config foneshare --tenant-id 762419
```

### 3. 实际执行更新
**⚠️ 注意：这会实际修改数据库！**
```bash
python update_opinions_from_csv.py --execute
```

### 4. 指定环境的实际执行
```bash
python update_opinions_from_csv.py --execute --config foneshare --tenant-id 762419
```

### 5. 使用简写参数
```bash
# 预览模式
python update_opinions_from_csv.py -p -c ceshi112 -t 71557

# 执行模式
python update_opinions_from_csv.py -e -c ceshi112 -t 71557
```

## 输出示例

### 预览模式输出
```
开始批量更新意见数据...
配置环境: ceshi112
租户ID: 71557
数据来源: 内嵌数据（无需外部CSV文件）
运行模式: 预览模式
--------------------------------------------------
读取到 53 条待更新数据
预览模式：跳过MongoDB连接
[1/53] 预览 - taskId:6560b35748e3ef522246e866, opinionId:6560b4ade08ae4469dc72d16
    新意见内容: 业务区跟进回款闭环
[2/53] 预览 - taskId:6560b5fb4db95f50a572270d, opinionId:6560b6d14db95f50a572272a
    新意见内容: 业务区跟进回款闭环
...
--------------------------------------------------
处理完成统计:
成功处理: 53 条
失败记录: 0 条
总计处理: 53 条

注意：这是预览模式，没有实际更新数据库。
如需实际执行更新，请使用 --execute 参数。
```

### 执行模式输出
```
开始批量更新意见数据...
配置环境: ceshi112
租户ID: 71557
数据来源: 内嵌数据（无需外部CSV文件）
运行模式: 实际更新
--------------------------------------------------
读取到 53 条待更新数据
MongoDB连接成功
[1/53] 更新成功 - taskId:6560b35748e3ef522246e866, opinionId:6560b4ade08ae4469dc72d16
[2/53] 更新成功 - taskId:6560b5fb4db95f50a572270d, opinionId:6560b6d14db95f50a572272a
...
--------------------------------------------------
处理完成统计:
成功处理: 53 条
失败记录: 0 条
总计处理: 53 条
```

## 内嵌数据说明

脚本中包含了53条更新记录，每条记录包含：
- `taskId`: 任务ID
- `opinionId`: 意见ID  
- `需要修改为`: 新的意见内容

数据格式示例：
```python
{"taskId": "6560b35748e3ef522246e866", "opinionId": "6560b4ade08ae4469dc72d16", "需要修改为": "业务区跟进回款闭环"}
```

## 安全建议

1. **先预览后执行**：始终先使用 `--preview` 模式查看将要更新的内容
2. **备份数据**：在执行实际更新前，建议备份相关数据
3. **小批量测试**：可以先修改脚本中的数据，进行小批量测试
4. **验证结果**：执行后验证更新结果是否符合预期

## 故障排除

### 常见错误

1. **MongoDB连接失败**
   - 检查网络连接
   - 确认配置环境是否正确
   - 验证租户ID是否有效

2. **更新失败**
   - 检查taskId和opinionId是否存在
   - 确认数据格式是否正确
   - 查看错误日志获取详细信息

3. **权限问题**
   - 确认有足够的数据库操作权限
   - 检查用户权限配置

## 帮助信息

查看完整的帮助信息：
```bash
python update_opinions_from_csv.py --help
```

## 版本历史

- v2.0: 移除pandas依赖，数据内嵌，添加预览模式
- v1.0: 初始版本，依赖pandas和外部CSV文件
