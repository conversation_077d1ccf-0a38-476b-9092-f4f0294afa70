# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import sys
import time
import requests
import json
import codecs
import os
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter


# 查询使用了6中审批角色的
reload(sys)
sys.setdefaultencoding('utf8')

# 配置信息 - 现在主要用于API调用
current_config = "ceshi112"
tenant_id = '71557'

# JSON文件路径配置
json_filename = "workflow_data_71557.json"


envs = {
    "foneshare": {
        "metadata_url": "http://172.17.4.230:30393/API",
        "fs_paas_org": "http://172.17.4.230:54833/fs-paas-org",
        "fs_paas_auth_biz": "http://172.17.4.196:14649/API",
        "fs_crm_metadata": "http://172.17.4.196:11740/fs-crm-metadata"
    },
    "ceshi112": {
        "metadata_url": "http://10.112.5.251:4859/API",
        "fs_paas_org": "http://10.112.5.251:27436/fs-paas-org",
        "fs_paas_auth_biz": "http://10.112.5.251:12503/API",
        "fs_crm_metadata": "http://10.112.5.251:25745/fs-crm-metadata"

    }
}
env = envs[current_config]

employee_cache = {}
department_cache = {}
group_cache = {}
role_cache = {}
approval_role_cache = {}
object_describe_cache = {}


def get_format_date(create_time):
    time_local = time.localtime(create_time / 1000)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_local)


def get_data_info(tenant_id, entity_id, data_id):
    url = env["metadata_url"] + "/API/v1/inner/rest/internal/" + entity_id + "/" + data_id

    querystring = {"includeDescribe": "false", "isFillExtendField": "false", "includeQuoteValue": "false",
                   "includeLookupName": "false", "includeStatistics": "false"}

    headers = {
        'content-type': "application/json",
        'x-fs-userinfo': "-10000",
        'x-fs-ei': tenant_id
    }

    response = requests.request("GET", url, headers=headers, params=querystring)
    return json.loads(response.text)["data"]["object_data"]


# 只获取对象apiname
def query_all_object_describe():
    url = env["metadata_url"] + '/v1/inner/rest/object_describe/all'

    headers = {
        'Content-Type': 'application/json',
        'x-fs-ei': tenant_id,
        'x-fs-userInfo': '-10000',
        'x-tenant-id': tenant_id,
        'x-user-id': '-10000'
    }

    response = requests.get(url, headers=headers)
    return json.loads(response.text)["data"]


# 获取人员信息
def query_employee_info_name(user_ids):
    query_user_ids = []
    user_name_str = ''
    for user_id in user_ids:
        employee = employee_cache.get(user_id)
        if employee is None:
            query_user_ids.append(user_id)
        else:
            if user_name_str == '':
                user_name_str = user_name_str + employee.get("name")
            else:
                user_name_str = user_name_str + "," + employee.get("name")

    if len(query_user_ids) == 0:
        return user_name_str

    url = env["fs_paas_org"] + '/org/employees/info/batch/out'

    headers = {
        'X-fs-Enterprise-Id': tenant_id,
        'Content-Type': 'application/json'
    }

    data = {
        "idList": query_user_ids,
        "tenantId": tenant_id,
        "userId": "-10000",
        "appId": "CRM"
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))
    result = json.loads(response.text)["result"]
    for it in result:
        employee_cache[it.get("id")] = it
        if user_name_str == '':
            user_name_str = user_name_str + it.get("name")
        else:
            user_name_str = user_name_str + "," + it.get("name")

    return user_name_str


# 获取部门信息
def query_department_info_name(department_ids):
    query_department_ids = []
    department_name_str = ''
    for department_id in department_ids:
        department = department_cache.get(department_id)
        if department is None:
            query_department_ids.append(department_id)
        else:
            if department_name_str == '':
                department_name_str = department_name_str + department.get("name")
            else:
                department_name_str = "," + department_name_str + department.get("name")

    if len(query_department_ids) == 0:
        return department_name_str

    url = env["fs_paas_org"] + '/org/dept/query/deptId'

    headers = {
        'X-fs-Enterprise-Id': tenant_id,
        'Content-Type': 'application/json'
    }

    data = {
        "deptIds": query_department_ids,
        "context": {
            "tenantId": tenant_id,
            "userId": "-10000",
            "appId": "CRM"
        }
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))
    result = json.loads(response.text)["result"]
    for it in result:
        department_cache[it.get("deptId")] = it
        if department_name_str == '':
            department_name_str = department_name_str + it.get("name")
        else:
            department_name_str = department_name_str + "," + it.get("name")

    return department_name_str


# 获取用户组信息
def query_group_info_name(group_ids):
    query_group_ids = []
    group_name_str = ''
    for group_id in group_ids:
        groupObject = group_cache.get(group_id)
        if groupObject is None:
            query_group_ids.append(group_id)
        else:
            if group_name_str == '':
                group_name_str = group_name_str + groupObject.get("name")
            else:
                group_name_str = group_name_str + "," + groupObject.get("name")

    if len(query_group_ids) == 0:
        return group_name_str

    url = env["fs_paas_org"] + '/org/group/list'

    headers = {
        'X-fs-Enterprise-Id': tenant_id,
        'Content-Type': 'application/json'
    }

    data = {
        "groupIdList": query_group_ids,
        "tenantId": tenant_id,
        "userId": "-10000",
        "appId": "CRM"
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))
    result = json.loads(response.text)["result"]
    for it in result:
        group_cache[it.get("id")] = it
        if group_name_str == '':
            group_name_str = group_name_str + it.get("name")
        else:
            group_name_str = group_name_str + "," + it.get("name")

    return group_name_str


def get_approval_role_info_name(approval_codes):
    approval_role_it_name_str = ''
    for approval_code in approval_codes:
        approval_role_object = approval_role_cache.get(approval_code)
        if approval_role_object != None:
            if approval_role_it_name_str == '':
                approval_role_it_name_str = approval_role_it_name_str + approval_role_object.get("roleName")
            else:
                approval_role_it_name_str = approval_role_it_name_str + "," + approval_role_object.get("roleName")

    return approval_role_it_name_str


def query_role_user_department_roles():
    url = env["fs_paas_auth_biz"] + "/v1/object/RoleUserDepartment/service/roles"
    headers = {
        'Accept-Language': 'zh-CN',
        'Content-Type': 'application/json',
        'X-fs-Enterprise-Id': tenant_id,
        'x-fs-ei': tenant_id,
        'x-fs-userInfo': '-10000',
        'x-tenant-id': tenant_id,
        'x-user-id': '-10000',
        'x-fs-employee-id': '-10000'
    }
    data = {
        "bizType": "ApprovalDepartment"
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    role_info_list = json.loads(response.text)["roleInfoList"]
    for role_info in role_info_list:
        approval_role_cache[role_info.get("roleCode")] = role_info


def get_role_info_by_tenant_id():
    url = env["fs_crm_metadata"] + "/crmrest/new_tenant_init/getRoleInfoByTenantId"
    params = {
        "tenantId": tenant_id
    }
    response = requests.get(url, params=params)
    result = json.loads(response.text)["result"]
    if result is not None:
        roles = result["roles"]
        for role in roles:
            role_cache[role.get("roleCode")] = role


def get_role_info_name(role_codes):
    role_it_name_str = ''
    for role_code in role_codes:
        role_object = role_cache.get(role_code)
        if role_object != None:
            if role_it_name_str == '':
                role_it_name_str = role_it_name_str + role_object.get("roleName")
            else:
                role_it_name_str = role_it_name_str + "," + role_object.get("roleName")

    return role_it_name_str


def get_describe(describe_api_name):
    describe = object_describe_cache.get(describe_api_name)
    if describe is not None:
        return describe

    url = env["metadata_url"] + "/v1/inner/rest/internal/object_describe/" + describe_api_name

    querystring = {"include_ref_describe": "true"}

    headers = {
        'content-type': "application/json",
        'x-fs-userinfo': "-10000",
        'x-fs-ei': tenant_id
    }

    response = requests.request("GET", url, headers=headers, params=querystring)
    describe = json.loads(response.text)["data"]["describe"]
    object_describe_cache[describe_api_name] = describe
    return describe


def clean_excel_value(value):
    """清理Excel单元格中的非法字符"""
    if value is None:
        return ""

    # 转换为字符串
    value_str = unicode(value) if not isinstance(value, unicode) else value

    # 移除Excel不支持的控制字符
    # Excel不支持ASCII控制字符（0-31），除了制表符(9)、换行符(10)、回车符(13)
    cleaned = ""
    for char in value_str:
        char_code = ord(char)
        if char_code < 32:
            # 保留制表符、换行符、回车符，其他控制字符替换为空格
            if char_code in (9, 10, 13):
                cleaned += char
            else:
                cleaned += " "
        else:
            cleaned += char

    return cleaned


def export_excel(data, filename="业务流定义导出.xlsx"):
    wb = Workbook()
    ws = wb.active
    ws.title = u"业务流定义导出"

    cell_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

    for row_idx, row_data in enumerate(data, start=1):
        for col_idx, value in enumerate(row_data, start=1):
            # 清理数据中的非法字符
            cleaned_value = clean_excel_value(value)
            cell = ws.cell(row=row_idx, column=col_idx, value=cleaned_value)
            cell.alignment = cell_alignment
            if row_idx == 1:
                cell.font = Font(bold=True)

    # 自动调整列宽
    # 设置固定列宽,避免过宽影响阅读
    for col_idx in range(1, ws.max_column + 1):
        col_letter = get_column_letter(col_idx)
        ws.column_dimensions[col_letter].width = 50

    wb.save(filename)


def get_task_type(node_config):
    type = node_config.get("type")
    if ('startEvent' == type):
        return "开始节点", type, ""
    elif ('userTask' == type):
        bpm_extension = node_config.get("bpmExtension")
        execution_type = bpm_extension.get("executionType")
        if ('update' == execution_type):
            return "业务活动", type, execution_type
        elif ('approve' == execution_type):
            return "审批节点", type, execution_type
        else:
            return "人工节点", type, execution_type
    elif ('executionTask' == type):
        remind = node_config.get("remind")
        if (remind == True):
            return "等待节点", type, ""
        else:
            return "自动节点", type, ""
    elif ('exclusiveGateway' == type):
        return "分支节点", type, ""
    elif ('parallelGateway' == type):
        return "并行节点", type, ""
    elif ('endEvent' == type):
        return "结束节点", type, ""
    elif ('latencyTask' == type):
        return "等待节点(HIS)", type, ""
    else:
        return node_config["name"], type, ""


def parse_variable_expr(expr):
    """
    解析类似 ${activity_1753078599478##object_RlhY0__c##field_2z320__c##field_ee220__c}
    返回 object_RlhY0__c,field_2z320__c,field_ee220__c
    """
    if expr.startswith("${") and expr.endswith("}"):
        expr = expr[2:-1]
    parts = expr.split("##")
    # 跳过第一个 activity_xxx
    return parts[1:]


def recursive_get_task_info(source_workflow_id, activities_map, transition_map, from_id, it_to_nodes, col_items,
                            exists_activity_ids, end_ids):
    exists_activity_ids.append(from_id)
    for to_node in it_to_nodes:

        node_id = to_node.get("id")
        if node_id == from_id:
            continue

        to_node_activity = activities_map.get(node_id)
        if to_node_activity is None:
            continue

        str = "节点名称:" + getString(to_node_activity, "name")
        descr = getString(to_node_activity, "description")
        if (len(descr) > 0):
            str = str + "(描述:" + descr + ")"

        data_ee = get_task_type(to_node_activity)
        if (data_ee == None):
            print("未获取到节点信息,sourceWorkflowId:%s,activityId:%s" % (source_workflow_id, node_id))
            continue
        type = data_ee[1]
        if type == 'userTask':
            str = get_user_task(str, to_node_activity)
        if type == 'exclusiveGateway':
            str = get_exclusive_gateway(str, to_node_activity)
        if type == 'executionTask':
            str = get_execution_task(str, to_node_activity)
        col_items.append(str)

        next_to_nodes = transition_map.get(node_id)
        if next_to_nodes is not None and len(next_to_nodes) > 0:
            next_list = []
            for it in next_to_nodes:
                if (it.get("id") not in exists_activity_ids) and (it.get("id") not in end_ids):
                    next_list.append(it)

            recursive_get_task_info(source_workflow_id, activities_map, transition_map, node_id, next_list, col_items,
                                    exists_activity_ids, end_ids)


def get_exclusive_gateway(str, to_node_activity):
    return str


def get_execution_task(str, to_node_activity):
    return str


def get_user_task(str, to_node_activity):
    assignee = to_node_activity.get("assignee")
    assignee_type = to_node_activity.get("assigneeType")
    if ('assigneeFunction' == assignee_type):
        assignee_function = to_node_activity.get("assigneeFunction")
        function_api_name = assignee_function.get('functionApiName')
        str = str + "\n函数解析处理人\n函数ApiName:" + function_api_name
        return str
    # 人员解析
    person = assignee.get("person")
    if person is not None:
        employee_name_str = query_employee_info_name(person)
        if len(employee_name_str) > 0:
            str = str + "\n选择处理人为:"
            str = str + employee_name_str
    # 部门解析
    dept = assignee.get("dept")  # 部门
    if dept is not None:
        department_name_str = query_department_info_name(dept)
        if len(department_name_str) > 0:
            str = str + "\n选择部门为:"
            str = str + department_name_str
    # 部门负责人 解析
    dept_leader = assignee.get("dept_leader")
    if dept_leader is not None:
        department_leader_name_str = query_department_info_name(dept_leader)
        if len(department_leader_name_str) > 0:
            str = str + "\n选择部门负责人为:"
            str = str + department_leader_name_str
    # 用户组 解析
    group = assignee.get("group")
    if group is not None:
        group_name_str = query_group_info_name(group)
        if len(group_name_str) > 0:
            str = str + "\n选择用户组为:"
            str = str + group_name_str
    # 发起人 解析
    applicant = assignee.get("applicant")
    if applicant is not None and len(applicant) > 0:
        str = str + "\n流程发起人"
    # 流程相关变量
    ext_bpm = assignee.get("ext_bpm")
    if ext_bpm is not None:
        for ext_bpm_item in ext_bpm:
            if ('instance##owner' in ext_bpm_item):
                str = str + "\n流程发起人"
            elif ('applicant##dept_leader' in ext_bpm_item):
                str = str + "\n流程发起人所属主部门负责人"
            elif ('applicant##leader' in ext_bpm_item):
                str = str + "\n流程发起人汇报对象"
            elif ('##assigneeId##leader' in ext_bpm_item):
                str = str + "\n节点处理人上级"
            elif ('##assigneeId##dept_leader' in ext_bpm_item):
                str = str + "\n节点处理人所属主部门负责人"
            elif ('##assigneeId' in ext_bpm_item):
                str = str + "\n节点处理人"
            elif ('##processorId##dept_leader' in ext_bpm_item):
                str = str + "\n任务执行人主部门负责人"
            elif ('##processorId##leader' in ext_bpm_item):
                str = str + "\n节点执行人上级"
            elif ('##processorId' in ext_bpm_item):
                str = str + "\n节点执行人"
            elif ('##owner' in ext_bpm_item):
                str = str + "\n数据负责人"
            elif ('##leader' in ext_bpm_item):
                str = str + "\n数据负责人汇报对象"
            elif ('##dept_leader' in ext_bpm_item):
                str = str + "\n数据负责人部门负责人"
            elif ('##group_leader' in ext_bpm_item):
                str = str + "\n记录相关团队成员上级(汇报对象)"
            elif ('##group_dept_leader' in ext_bpm_item):
                str = str + "\n记录相关团队成员所在主部门负责人"
            elif ('##outer_data_group' in ext_bpm_item):
                str = str + "\n外部相关团队成员"
            elif ('##group_dept' in ext_bpm_item):
                str = str + "\n数据相关团队成员_部门"
            elif ('##group_role' in ext_bpm_item):
                str = str + "\n数据相关团队成员_角色"
            elif ('##group' in ext_bpm_item):
                str = str + "\n记录相关团队成员"
            elif ('##dept_field' in ext_bpm_item):
                str = str + "\n数据负责人所在主部门人员字段"
            elif ('##out_owner_dept_leader' in ext_bpm_item):
                str = str + "\n外部负责人主属部门负责人"
            else:
                str = "未知" + ext_bpm_item
    # 审批角色 解析
    approval_role = assignee.get("approval_role")
    if (approval_role != None):
        approval_role_name_str = get_approval_role_info_name(approval_role)
        if len(approval_role_name_str) > 0:
            str = str + "\n选择审批角色为:"
            str = str + approval_role_name_str
    # 角色解析
    role = assignee.get("role")
    if role is not None:
        role_name_str = get_role_info_name(role)
        if len(role_name_str) > 0:
            str = str + "\n选择角色为:"
            str = str + role_name_str
    ext_process = assignee.get("ext_process")  # 扩展字段,6.5适配业务流
    if (ext_process != None):
        # str = str + "\n选择对象人员字段为:"
        for ep in ext_process:
            rst = parse_variable_expr(ep)
            entity_id = rst[0]
            main_field = rst[1]
            if (main_field == 'dept_field_junior_assistant_id'):
                str = str + "\n" + "部门初级助理"
                continue
            if (main_field == 'dept_field_assistant_id'):
                str = str + "\n" + "部门助理"
                continue
            lockup_field = ''
            if (len(rst) > 2):
                lockup_field = rst[2]
            main_describe = get_describe(entity_id)
            display_name = main_describe.get("display_name")
            lockup_field_label = ''
            fields = main_describe.get("fields")
            main_field_describe = fields.get(main_field)
            if (main_field_describe is not None):
                main_field_label = main_field_describe.get("label")
                if (lockup_field is not None):
                    main_field_type = main_field_describe.get("type")
                    if main_field_type == 'object_reference':
                        target_api_name = main_field_describe.get('target_api_name')
                        target_api_name_describe = get_describe(target_api_name)
                        target_fields = target_api_name_describe.get("fields")
                        target_lockup_field = target_fields.get(lockup_field)
                        if (target_lockup_field is not None):
                            lockup_field_label = target_lockup_field.get("label")
                str = str + "\n" + display_name + "." + main_field_label + "." + lockup_field_label
    extUser_type = assignee.get("extUserType")  # 人员扩展字段,6.5适配业务流
    if (extUser_type != None):
        # str = str + "\n选择对象人员字段为:"
        for ep in extUser_type:
            rst = parse_variable_expr(ep)
            entity_id = rst[0]
            main_field = rst[1]
            lockup_field = ''
            if (len(rst) > 2):
                lockup_field = rst[2]
            main_describe = get_describe(entity_id)
            display_name = main_describe.get("display_name")
            lockup_field_label = ''
            fields = main_describe.get("fields")
            main_field_describe = fields.get(main_field)
            if (main_field_describe is not None):
                main_field_label = main_field_describe.get("label")
                if (lockup_field is not None):
                    main_field_type = main_field_describe.get("type")
                    if main_field_type == 'object_reference':
                        target_api_name = main_field_describe.get('target_api_name')
                        target_api_name_describe = get_describe(target_api_name)
                        target_fields = target_api_name_describe.get("fields")
                        target_lockup_field = target_fields.get(lockup_field)
                        if (target_lockup_field is not None):
                            lockup_field_label = target_lockup_field.get("label")
                str = str + "\n" + display_name + "." + main_field_label + "." + lockup_field_label
    external_role = assignee.get("external_role")  # 外部角色
    if (external_role is not None):
        str = str + "\n互联角色:"
        for er in external_role:
            str = str + er + ","
    return str


def getString(data, key):
    value = data.get(key)
    if value == None:
        return ""
    else:
        return value


query_role_user_department_roles()
get_role_info_by_tenant_id()

def load_workflows_from_json(filename):
    """从JSON文件加载workflow数据"""
    try:
        if not os.path.exists(filename):
            print "错误: JSON文件 %s 不存在" % filename
            return []

        with codecs.open(filename, 'r', encoding='utf-8') as f:
            workflows = json.load(f)

        print "成功从 %s 加载了 %d 个业务流" % (filename, len(workflows))
        return workflows
    except Exception as e:
        print "读取JSON文件时发生错误: %s" % str(e)
        return []


if __name__ == '__main__':
    # 从JSON文件读取workflow数据
    workflows = load_workflows_from_json(json_filename)

    if not workflows:
        print "没有找到业务流数据，程序退出"
        exit(1)

    # 初始化MongoDB管理器 - 仍需要用于API调用
    object_describe_list = query_all_object_describe()
    object_describe_map = {}
    for object_describe in object_describe_list:
        describe_api_name = object_describe.get("describeApiName")
        object_describe_map[describe_api_name] = object_describe

    items = []

    print "开始处理 %d 个业务流..." % len(workflows)

    for workflow in workflows:
        activities_map = {}
        transition_map = {}
        exists_activity_ids = []
        if (len(items) > 300):
            break
        col_items = []
        # 每列的展示信息

        workflow_name = workflow.get('name', '')
        print "正在处理业务流: %s" % workflow_name

        workflow_description = workflow.get('description', '')
        entity_id = workflow.get('entityId', '')
        source_workflow_id = workflow.get('sourceWorkflowId', '')
        link_app_enable = workflow.get('linkAppEnable', '')
        linkAppName = workflow.get('linkAppName', '')
        create_time = workflow.get('createTime', '')
        creator = workflow.get('creator', '')
        modifier = workflow.get('modifier', '')
        modify_time = workflow.get('modifyTime', '')
        activities = workflow.get('activities', [])
        transitions = workflow.get('transitions', [])

        # 确保是列表类型（现在应该不需要字符串解析了）
        if not isinstance(activities, list):
            print "警告: activities 不是列表类型，跳过业务流: %s" % workflow_name
            continue
        if not isinstance(transitions, list):
            print "警告: transitions 不是列表类型，跳过业务流: %s" % workflow_name
            continue

        first_col = ('流程名称:%s\n'
                     # 'ApiName:%s\n'
                     '发起对象:%s\n'
                     # '创建人:%s\n'
                     # '创建时间:%s\n'
                     # '修改人:%s\n'
                     # '修改时间:%s'
                     ) % (workflow_name,
                          # source_workflow_id,
                          object_describe_map.get(entity_id).get("describeDisplayName") if object_describe_map.get(
                              entity_id) is not None else None,
                          # query_employee_info_name([creator]),
                          # get_format_date(create_time),
                          # query_employee_info_name([modifier]),
                          # get_format_date(modify_time)
                          )
        col_items.append(first_col)

        start_id = ''
        end_ids = []

        print "  - 活动节点数量: %d, 转换数量: %d" % (len(activities), len(transitions))

        for activity in activities:
            activity_id = activity.get("id")
            activity_type = activity.get("type")
            if activity_type == 'startEvent':
                start_id = activity_id
            if activity_type == 'endEvent':
                end_ids.append(activity_id)
            activities_map[activity_id] = activity

        for transition in transitions:
            from_id = transition.get("fromId")
            to_id = transition.get("toId")

            from_map = transition_map.get(from_id)
            if from_map is None:
                from_map = []
            from_map.append(activities_map.get(to_id))
            transition_map[from_id] = from_map

        start_node = activities_map.get(start_id)

        start_str = "节点名称:" + getString(start_node, "name")
        descr = getString(start_node, "description")
        if (len(descr) > 0):
            start_str = start_str + "(描述:" + descr + ")"
        col_items.append(start_str)

        # 获取开始节点之后的节点
        to_nodes = transition_map.get(start_id)
        recursive_get_task_info(source_workflow_id, activities_map, transition_map, start_id, to_nodes, col_items,
                                exists_activity_ids, end_ids)

        for end in end_ids:
            end_node = activities_map.get(end)

            end_str_item = "节点名称:" + getString(end_node, "name")
            descr = getString(end_node, "description")
            if (len(descr) > 0):
                end_str_item = end_str_item + "(描述:" + descr + ")"
            col_items.append(end_str_item)

        items.append(col_items)

    print "处理完成，共处理了 %d 个业务流，开始导出Excel..." % len(items)
    export_excel(items)
    print "Excel导出完成！"
