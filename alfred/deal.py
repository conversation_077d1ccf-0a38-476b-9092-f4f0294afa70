import json

# Read data from data.txt file
with open("data.txt", "r") as file:
    data = json.load(file)

candidate_counts = {}

# Iterate through the data and calculate cumulative count for each unique candidate ID
for entry in data:
    for candidate_id in entry["current_candidate_ids"]:
        if candidate_id in candidate_counts:
            candidate_counts[candidate_id] += entry["count"]
        else:
            candidate_counts[candidate_id] = entry["count"]

# Sort the candidate_counts dictionary by candidate ID
sorted_candidate_counts = dict(sorted(candidate_counts.items()))

# Output the sorted candidate_counts line by line
total = 0
for candidate_id, count in sorted_candidate_counts.items():
    total += count
    print(candidate_id + "," + str(count))

print total
