# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import json
import sys
import datetime
from os import listdir
import os
import pathlib
# from pathlib import Path
from os.path import isdir, join, expanduser

from workflow import Workflow3



reload(sys)  # Python2.5 初始化后会删除 sys.setdefaultencoding 这个方法，我们需要重新载入
sys.setdefaultencoding('utf-8')

wf = Workflow3()
log = wf.logger

dir = "/Users/<USER>/workspace/paw-workspace"


#  查询出当前目录下 存在有.git 目录的子目录
# nohup python query_exist_instance_not_found_task.py > query_exist_instance_not_found_task.log 2>&1 &
# pip install Alfred-Workflow


def get_current_git_dirs():
    # 保存路径
    dirs = []
    # 注意 ~类型的路径
    path = expanduser(dir)
    values = [f for f in listdir(path) if isdir(join(path, f))]
    log.debug("---->" + str(values))
    for dr in values:
        new_father_path = dir + "/" + dr
        new_f_path = expanduser(new_father_path)
        new_f_path_kid = [f for f in listdir(new_f_path) if isdir(join(new_f_path, f))]
        for drc in new_f_path_kid:
            log.debug(new_father_path + "/" + drc)
            # 把path中包含的"~"和"~user"转换成用户目录
            dirs.append(expanduser(new_father_path + "/" + drc))

    return dirs


def get_path(wf):
    search_key = wf.args[0]
    log.debug('search_key: ' + search_key)
    file_list = wf.cached_data('project', get_current_git_dirs, max_age=60)
    # 根据query过滤目录
    for x in file_list:
        log.info("x:" + x)
        if (search_key and (search_key in x)):
            # /Users/<USER>/workspace/fxiaoke/fs-bpm-rest-proxy
            file_name = os.path.split(x)[1]
            # print(filename)

            wf.add_item(title=file_name, arg=x, valid=True)

    wf.send_feedback()


if __name__ == '__main__':
    # dir = "/Users/<USER>/workspace"
    # dirs = get_current_git_dirs(dir)
    sys.exit(wf.run(get_path))
