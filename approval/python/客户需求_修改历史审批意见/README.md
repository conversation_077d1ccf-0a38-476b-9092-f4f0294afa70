# 查询审批意见脚本使用说明

## 概述

`query_opinions_by_object_data_id.py` 是一个用于查询审批意见的脚本，支持通过命令行参数灵活配置查询条件。

## 功能特性

- 支持多个对象数据ID查询
- 支持任务名称过滤
- 支持用户ID过滤
- 支持不同环境配置切换
- 兼容Python 2.6+

## 命令行参数

| 参数 | 简写 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--config` | `-c` | 否 | foneshare | 配置环境 (foneshare/ceshi112) |
| `--tenant-id` | `-t` | 否 | 762419 | 租户ID |
| `--object-data-ids` | `-o` | 是 | 无 | 对象数据ID列表，多个ID用逗号分隔 |
| `--task-name` | `-n` | 否 | 空 | 任务名称过滤条件 |
| `--user-id` | `-u` | 否 | 空 | 用户ID或用户名过滤条件，支持用户ID(如:2571)或用户名(如:胡晶) |

## 使用示例

### 1. 基本用法
查询单个对象数据ID的所有审批意见：
```bash
python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577
```

### 2. 查询多个对象数据ID
```bash
python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577,6882fbb0fa6a8900074f1ec4
```

### 3. 指定任务名称过滤
只查询特定任务名称的审批意见：
```bash
python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577 --task-name "审批任务"
```

### 4. 指定用户ID过滤
只查询特定用户的审批意见（支持用户ID）：
```bash
python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577 --user-id 2571
```

### 5. 通过用户名过滤
只查询特定用户的审批意见（支持用户名）：
```bash
python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577 --user-id 胡晶
```

### 5. 组合过滤条件
同时指定任务名称和用户ID：
```bash
python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577 --task-name "审批任务" --user-id 2571
```

### 6. 切换到测试环境
```bash
python query_opinions_by_object_data_id.py --config ceshi112 --tenant-id 71557 --object-data-ids 6882fbb0fa6a8900074f1ec4
```

### 7. 使用简写参数
```bash
python query_opinions_by_object_data_id.py -c ceshi112 -t 71557 -o 6882fbb0fa6a8900074f1ec4 -n "审批任务" -u 2571
```

## 输出格式

脚本会输出以下信息：
- 配置信息（环境、租户ID、过滤条件等）
- 审批意见详情，包括：
  - objectId: 对象数据ID
  - taskId: 任务ID
  - 节点名称: 审批节点名称
  - opinionId: 意见ID
  - 原意见: 审批意见内容
  - 审批时间: 审批时间
  - 审批状态: 审批动作类型
  - feedId: Feed ID
  - replyId: Reply ID
  - userId: 用户ID
  - userName: 用户名称

## 注意事项

1. `--object-data-ids` 是必需参数，必须提供至少一个对象数据ID
2. 多个对象数据ID用逗号分隔，不要有空格
3. 如果不指定过滤条件，将返回所有匹配的审批意见
4. 用户名称映射在脚本中预定义，如需添加新用户请修改 `users` 字典

## 帮助信息

查看完整的帮助信息：
```bash
python query_opinions_by_object_data_id.py --help
```

## 版本历史

- v2.0: 添加命令行参数支持，提高脚本灵活性
- v1.0: 初始版本，硬编码参数
