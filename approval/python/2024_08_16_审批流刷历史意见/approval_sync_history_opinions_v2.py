# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import pymongo
import sys
from bson import ObjectId
import requests
import json
import traceback
from engine_route_manager import MongoEngineManager

# select * from biz_flow_task_operate_log where id = '6881086c796365eee4eaaaef';
# nohup python approval_sync_history_opinions_v2.py > 733452_3.log 2>&1 &


# 查询使用了6中审批角色的
reload(sys)
sys.setdefaultencoding('utf8')

# 预初始化企业
config = {
    "foneshare": {
        "apibus_url": "http://172.17.4.230:30393/API/v1",
    },
    "ceshi112": {
        "apibus_url": "http://10.112.5.251:16903/API/v1",
    }
}

update = True
current_config = "foneshare"
tenant_ids = ["733452"]


def get_data_info(entity_id, object_id):
    try:
        url = config[current_config]["apibus_url"] + "/inner/rest/object_data/" + entity_id + "/" + object_id
        headers = {
            'content-type': "application/json",
            'x-fs-userinfo': "-10000",
            'x-fs-ei': tenant_id,
            'x-tenant-id': tenant_id,
            'x-user-id': "-10000",
            'FSR-GRAY_VALUE': tenant_id,
            'X-fs-Enterprise-Id': tenant_id
        }
        query_string = {
            "includeInvalid": "false",
            "includeDeleted": "false",
            "includeDescribe": "false"
        }
        response = requests.request("GET", url, headers=headers, params=query_string)

        result = json.loads(response.text)
        if result.get("code") == 0:
            return json.loads(response.text)["data"]["object_data"]
        else:
            return None
    except Exception, error:
        print tenant_id + "get_metadata_data break,ignore,tenant_id:%s,entity_id:%s,object_id:%s,%s" % (
            tenant_id, entity_id, object_id, repr(error))
        return None


# 创建数据
def create_metadata_data(entity_id, data):
    if not update:
        print("debug模式-创建数据到对象侧,entity_id:%s,data:%s" % (entity_id, str(data)))
        return

    try:
        url = config[current_config]["apibus_url"] + "/inner/rest/object_data/" + entity_id

        headers = {
            'content-type': "application/json",
            'x-fs-userinfo': "-10000",
            'x-fs-ei': tenant_id,
            'x-tenant-id': tenant_id,
            'x-user-id': "-10000",
            'FSR-GRAY_VALUE': tenant_id,
            'X-fs-Enterprise-Id': tenant_id
        }

        response = requests.request("POST", url, data=json.dumps(data), headers=headers)
        print response.text
    except Exception, error:
        print tenant_id + "create_metadata_data break,ignore,tenant_id:%s,entity_id:%s,object_id:%s,%s" % (
            tenant_id, entity_id, id, repr(error))


def new_opinion_entity(task, opinion):
    return {
        "_id": opinion["id"],
        "task_id": str(task["_id"]),
        "task_api_name": "ApprovalTaskObj",
        "activity_id": task.get("activityId", ""),
        "workflow_id": task["workflowId"],
        "source_workflow_id": task["sourceWorkflowId"],
        "workflow_instance_id": task["workflowInstanceId"],
        "workflow_instance_api_name": "ApprovalInstanceObj",
        "reply_time": opinion.get("replyTime", 0),
        "reply_id": opinion.get("replyId", ""),
        "feed_id": opinion.get("feedId", ""),
        "operator_id": [opinion.get("userId", "")],
        "action_type": opinion.get("actionType", ""),
        "opinion": opinion.get("opinion", ""),
        "history": opinion.get("history", None),
        "auto_agree_type": opinion.get("autoAgreeType", "")
    }


# select * from biz_flow_task_operate_log where tenant_id = '705221'|=> create_time = 1681039284317 (2023-04-09 19:21:24),需要刷这之前的

# select count(1) from approval_task where tenant_id = '705221' and create_time < 1681056000000   56886条任务的审批意见需要同步
def query_history_task_and_update_opinions(tenant_id):
    manager = MongoEngineManager(current_config)
    engine_tdb = manager.get_engine_db(tenant_id, read_preference=pymongo.read_preferences.ReadPreference.SECONDARY)

    tasks = engine_tdb.tasks.find({
        "tenantId": tenant_id,
        "type": "approvalflow",
        "appId": "CRM",
        "state": "pass",
        "opinions": {"$exists": True},
        "createTime": {"$lte": 1681056000000}  # tt 2023-04-10 00:00:00  即:处理4月10日之前的数据
    }).batch_size(100)
    for task in tasks:
        _task_id = task.get("_id")
        task_id = str(_task_id)
        try:

            opinions = task.get("opinions")
            id_not_found_id_list = []

            for opinion in opinions:
                opinion_id = opinion.get("id")
                if opinion_id is None:
                    opinion["id"] = str(ObjectId())
                    id_not_found_id_list.append(opinion["id"])

            if update:
                if len(id_not_found_id_list) > 0:
                    engine_tdb.tasks.update({"_id": _task_id}, {"$set": {"opinions": opinions}})
                    print("更新db数据,taskId:%s,opinions:%s" % (task_id, str(opinions)))
            else:
                if len(id_not_found_id_list) > 0:
                    print("debug模式-更新db数据,taskId:%s,opinions:%s" % (task_id, str(opinions)))

            # 重新遍历一次
            for opinion in opinions:
                opinion_id = opinion["id"]
                if opinion_id in id_not_found_id_list:
                    opinion_entity = new_opinion_entity(task, opinion)
                    create_metadata_data("ApproverOpinionObj", opinion_entity)
                else:
                    opinion_data = get_data_info("ApproverOpinionObj", opinion_id)
                    if opinion_data is None:
                        opinion_entity = new_opinion_entity(task, opinion)
                        create_metadata_data("ApproverOpinionObj", opinion_entity)
        except Exception as error:
            print tenant_id + " 同步异常,ignore,taskId:%s,%s" % (task_id, repr(error))
            traceback.print_exc()


if __name__ == '__main__':
    processed_tenant_ids = []

    count = 0.0
    for tenant_id in tenant_ids:
        count = count + 1
        if tenant_id not in processed_tenant_ids:
            print("%s  start %d" % (tenant_id, count * 100 / len(tenant_ids)))
            try:
                query_history_task_and_update_opinions(tenant_id)
            except Exception, e:
                print tenant_id + " break,ignore,%s" % repr(e)
                processed_tenant_ids.append(tenant_id)
        print("%s  started %d" % (tenant_id, count * 100 / len(tenant_ids)))
    print("it's ok! %s" % processed_tenant_ids)
