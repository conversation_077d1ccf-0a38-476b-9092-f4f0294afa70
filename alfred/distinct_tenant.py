import json

import requests

set_tenant_ids = set()

ok_tenant_ids = set()

gray_tenant_ids = ["627067", "481507", "444621", "392985", "525174", "409339", "439389", "511608", "77747", "61718",
                   "749177", "744796", "534058", "147597", "78835", "459307", "535960", "744375", "388380", "412715",
                   "402491", "476429", "612081", "326364", "712201", "72979", "469035", "365025", "303418", "467796",
                   "519777", "625697", "452322", "634697", "610249", "371628", "387173", "83703", "473048", "503016",
                   "244490", "593158", "539064", "76894", "319795", "424210", "505539", "442998", "477689", "354584",
                   "537302", "481559", "590063", "326970", "412845", "66611", "436911", "452412", "353810", "438859",
                   "419840", "43486", "218217", "261932", "528965", "73378", "83733", "539616", "372222", "330871",
                   "361912", "458539", "442211", "538314", "71286", "401098", "388176", "531707", "473018", "520153",
                   "100444", "77910", "339821", "499390", "723903", "334194", "441820", "473049", "477095", "322627",
                   "336708", "534035", "428185", "400678", "416044", "461541", "413691", "83232", "389468", "322975",
                   "251393", "338942", "499438", "69804", "80383", "369354", "536752", "142346", "419488", "510701",
                   "385793", "536983", "74889", "492630", "484966", "476547", "479842", "399581", "507485", "522289",
                   "440017", "388320", "377571", "251350", "93814", "452923", "477484", "174741", "604515", "452276",
                   "434249", "387594", "535408", "359702", "522688", "99655"]


def deal_tenant():
    tenant_ids = open("tenant_ids_data.txt", "rw").readlines()
    for tenant in tenant_ids:
        tenant_id_split = tenant.split(",")
        for ten in tenant_id_split:
            set_tenant_ids.add(ten)
    for tent_id in set_tenant_ids:
        if (tent_id not in gray_tenant_ids):
            print  tent_id


if __name__ == '__main__':
    deal_tenant()
