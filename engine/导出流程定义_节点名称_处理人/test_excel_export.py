# -*- coding: utf-8 -*-
"""
测试Excel导出功能的兼容性
"""

import sys
import os

# 添加根目录到路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root_dir)

# 导入修改后的模块中的函数
try:
    # 尝试使用xlwt (Python 2.6兼容)
    import xlwt
    USE_XLWT = True
    print("使用xlwt库进行Excel导出")
except ImportError:
    # 如果xlwt不可用，尝试使用openpyxl的兼容版本
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment
        from openpyxl.utils import get_column_letter
        USE_XLWT = False
        print("使用openpyxl库进行Excel导出")
    except ImportError:
        print("请安装xlwt库: pip install xlwt")
        sys.exit(1)

def clean_excel_value(value):
    """清理Excel单元格中的非法字符"""
    if value is None:
        return ""

    # 转换为字符串
    value_str = unicode(value) if not isinstance(value, unicode) else value

    # 移除Excel不支持的控制字符
    # Excel不支持ASCII控制字符（0-31），除了制表符(9)、换行符(10)、回车符(13)
    cleaned = ""
    for char in value_str:
        char_code = ord(char)
        if char_code < 32:
            # 保留制表符、换行符、回车符，其他控制字符替换为空格
            if char_code in (9, 10, 13):
                cleaned += char
            else:
                cleaned += " "
        else:
            cleaned += char

    return cleaned

def export_excel_xlwt(data, filename="test_export.xls"):
    """使用xlwt库导出Excel (Python 2.6兼容)"""
    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet(u'测试导出')
    
    # 设置样式
    header_style = xlwt.XFStyle()
    header_font = xlwt.Font()
    header_font.bold = True
    header_style.font = header_font
    
    # 设置对齐方式
    alignment = xlwt.Alignment()
    alignment.horz = xlwt.Alignment.HORZ_LEFT
    alignment.vert = xlwt.Alignment.VERT_CENTER
    alignment.wrap = 1  # 自动换行
    
    normal_style = xlwt.XFStyle()
    normal_style.alignment = alignment
    
    header_style.alignment = alignment
    
    for row_idx, row_data in enumerate(data):
        for col_idx, value in enumerate(row_data):
            # 清理数据中的非法字符
            cleaned_value = clean_excel_value(value)
            if row_idx == 0:  # 标题行
                ws.write(row_idx, col_idx, cleaned_value, header_style)
            else:
                ws.write(row_idx, col_idx, cleaned_value, normal_style)
    
    # 设置列宽 (xlwt使用不同的单位)
    for col_idx in range(len(data[0]) if data else 0):
        ws.col(col_idx).width = 256 * 30  # 256是xlwt的单位转换因子
    
    wb.save(filename)
    print("Excel文件已保存: %s" % filename)

def export_excel_openpyxl(data, filename="test_export.xlsx"):
    """使用openpyxl库导出Excel"""
    wb = Workbook()
    ws = wb.active
    ws.title = u"测试导出"

    cell_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

    for row_idx, row_data in enumerate(data, start=1):
        for col_idx, value in enumerate(row_data, start=1):
            # 清理数据中的非法字符
            cleaned_value = clean_excel_value(value)
            cell = ws.cell(row=row_idx, column=col_idx, value=cleaned_value)
            cell.alignment = cell_alignment
            if row_idx == 1:
                cell.font = Font(bold=True)

    # 自动调整列宽
    # 设置固定列宽,避免过宽影响阅读
    for col_idx in range(1, ws.max_column + 1):
        col_letter = get_column_letter(col_idx)
        ws.column_dimensions[col_letter].width = 30

    wb.save(filename)
    print("Excel文件已保存: %s" % filename)

def export_excel(data, filename="test_export.xlsx"):
    if USE_XLWT:
        export_excel_xlwt(data, filename.replace('.xlsx', '.xls'))
    else:
        export_excel_openpyxl(data, filename)

if __name__ == '__main__':
    # 创建测试数据
    test_data = [
        [u"流程名称", u"节点信息", u"处理人信息"],
        [u"测试流程1", u"开始节点", u"系统自动"],
        [u"测试流程1", u"审批节点", u"张三,李四"],
        [u"测试流程1", u"结束节点", u"系统自动"],
        [u"测试流程2", u"开始节点", u"系统自动"],
        [u"测试流程2", u"业务活动节点", u"王五"],
        [u"测试流程2", u"结束节点", u"系统自动"]
    ]
    
    print("开始测试Excel导出功能...")
    export_excel(test_data, "test_export.xlsx")
    print("测试完成！")
