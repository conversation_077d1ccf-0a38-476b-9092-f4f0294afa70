# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import sys
import time
import json
import codecs
import os

# 添加父目录到路径，以便导入 engine_route_manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from engine_route_manager import MongoEngineManager

# 查询使用了6中审批角色的
reload(sys)
sys.setdefaultencoding('utf8')

current_config = "ceshi112"
tenant_id = '71557'
source_workflow_id = '687dea6ff7eb370001f24c94'

# source_workflow_id = ''

# current_config = "foneshare"
# tenant_id = '529955'
# source_workflow_id = '687e641c6bb6dc000152e610'

envs = {
    "foneshare": {
        "metadata_url": "http://172.17.4.230:30393/API",
        "fs_paas_org": "http://172.17.4.230:54833/fs-paas-org",
        "fs_paas_auth_biz": "http://172.17.4.196:14649/API",
        "fs_crm_metadata": "http://172.17.4.196:11740/fs-crm-metadata"
    },
    "ceshi112": {
        "metadata_url": "http://10.112.5.251:4859/API",
        "fs_paas_org": "http://10.112.5.251:27436/fs-paas-org",
        "fs_paas_auth_biz": "http://10.112.5.251:12503/API",
        "fs_crm_metadata": "http://10.112.5.251:25745/fs-crm-metadata"

    }
}
env = envs[current_config]

if __name__ == '__main__':
    # 初始化MongoDB管理器
    manager = MongoEngineManager(current_config)
    mongo_config = manager.get_engine_db(tenant_id)

    if source_workflow_id != '':
        workflows = mongo_config.workflows.find({
            "tenantId": tenant_id,
            "appId": "BPM",
            "type": "workflow_bpm",
            "deleted": {"$exists": False},
            "history": False,
            "sourceWorkflowId": source_workflow_id
        })
    else:
        workflows = mongo_config.workflows.find({
            "tenantId": tenant_id,
            "appId": "BPM",
            "type": "workflow_bpm",
            "deleted": {"$exists": False},
            "history": False
        })

    # 收集所有workflow数据
    workflow_data_list = []

    def json_serializer(obj):
        """自定义JSON序列化器，只转换ObjectId等特殊类型"""
        if hasattr(obj, '__str__') and 'ObjectId' in str(type(obj)):
            return str(obj)
        raise TypeError("Object of type %s is not JSON serializable" % type(obj))

    for workflow in workflows:
        # 直接使用dict()转换，保持原始数据结构
        workflow_dict = dict(workflow)
        workflow_data_list.append(workflow_dict)

        # 打印workflow基本信息
        workflow_name = workflow.get('name', 'Unknown')
        source_workflow_id = workflow.get('sourceWorkflowId', 'Unknown')
        print "处理业务流: %s (ID: %s)" % (workflow_name, source_workflow_id)

    # 将数据写入JSON文件
    output_filename = "workflow_data_%s.json" % (tenant_id)

    try:
        # Python 2 使用 codecs 模块处理 UTF-8 编码
        with codecs.open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(workflow_data_list, f, ensure_ascii=False, indent=2, default=json_serializer)

        print "\n成功导出 %d 个业务流数据到文件: %s" % (len(workflow_data_list), output_filename)

        # 同时创建一个可读性更好的格式化文件
        readable_filename = "workflow_readable_%s.txt" % (tenant_id)

    except Exception as e:
        print "写入文件时发生错误: %s" % str(e)
