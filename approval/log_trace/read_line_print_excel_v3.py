# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import urllib.request
import urllib.error
import requests
import datetime
import json
import sys
from datetime import datetime as dt, timedelta
import time
import re
from urllib.parse import urlparse
import os
import platform
from dateutil.parser import parse

from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter
from dateutil.relativedelta import relativedelta
import importlib

# 兼容Python3的重载
importlib.reload(sys)

# 修改时间点 从start_date_str到end_date_str之间的告警 [2023-07-18,2023-07-19]
start_date_str = '2025-07-01'
end_date_str = '2025-07-03'
biz_name = 'APPROVAL'

# 排除某个日志,比如2月有29号;或者已经知道29日没有异常
exclusions_date = [
]
# 指在查询clickvisual时,是否将biz_name相关的appName拼进去,例如当biz_name="APPROVAL"时,将fs-crm-workflow拼进去。
use_service_name_append_sql = False

# 跳过某个企业的查询,比如漏扫企业,传递的参数稀奇古怪,一天可能有3000+异常请求,直接跳过
skip_tenant_ids = []

keys = {
    "BPM": {
        "name": "业务流",
        "service": "fs-bpm"
    },
    "FLOW": {
        "name": "流程公用服务",
        "service": "fs-flow"
    },
    "STAGE": {
        "name": "阶段推进器",
        "service": "fs-stage-propeller"
    },
    "APPROVAL": {
        "name": "审批流",
        "service": "fs-crm-workflow"
    },
    "PROCESS": {
        "name": "工作流",
        "service": "fs-workflow"
    },
    "CRM-FLOW": {
        "name": "WhatList",
        "service": ""
    },
}

tenant_config = {}

folder_name = 'config'
file_names = [f for f in os.listdir(folder_name) if f.endswith(".json")]
# file_names = []

# 遍历每个 JSON 文件并读取其内容
for file_name in file_names:
    file_path = os.path.join(folder_name, file_name)
    with open(file_path, "r", encoding='utf-8') as file:
        data = json.load(file)
        tenant_config[file_name] = data


def get_cloud_config(tenant_id):
    default_config = {}
    if tenant_id is not None and len(tenant_id) > 0:
        for item_config in tenant_config:
            config = tenant_config[item_config]
            if "foneshare.json" == item_config:
                default_config = config
                continue
            if int(tenant_id) in config["tenantIds"]:
                return config
    return default_config


cookie = "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218cd388a0e92b3-0022f23c5a631d-1f525637-2007040-18cd388a0eabcd%22%2C%22%24device_id%22%3A%2218cd388a0e92b3-0022f23c5a631d-1f525637-2007040-18cd388a0eabcd%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D"

biz_zh_name = keys[biz_name]["name"]
service_name = keys[biz_name]["service"]
# 以下信息无需修改
witer_excel_file_data_backup = []
url = 'https://oss.foneshare.cn/kibana01/api/console/proxy?path=log-cep-%2A%2F_search&method=GET'
title = []
title.append("时间")
title.append("traceId")
title.append("请求URL")
title.append("ClickVisual")
title.append("错误原因")
title.append("所属云")
title.append("日志备份")
witer_excel_file_data_backup.append(title)

#               时间,  traceId   ,请求url,ClickVisual,错误原因,服务号分析
column_widths = [20, 40, 70, 20, 60, 30, 15, 200]

wb = Workbook()
ws = wb.active
ws.title = "CEP异常日志统计"


def export_excel(data):
    if len(data) == 1:
        print("没有数据需要导出！")
        return
    # 设置列宽
    for col, width in enumerate(column_widths, start=1):
        column_letter = get_column_letter(col)  # 将列索引转换为字母
        ws.column_dimensions[column_letter].width = width

    # 设置超链接样式
    url_font = Font(color='0563C1', underline='single')
    url_alignment = Alignment(horizontal='center')

    # 写入数据并处理超链接
    for row, row_data in enumerate(data, start=1):  # openpyxl 从1开始索引
        for col, value in enumerate(row_data, start=1):
            if col == 4 and row != 1:  # openpyxl 从1开始索引
                if value:
                    # 在 openpyxl 中添加超链接
                    ws.cell(row=row, column=col).hyperlink = value
                    ws.cell(row=row, column=col).value = "点击访问链接"
                    ws.cell(row=row, column=col).font = url_font
                    ws.cell(row=row, column=col).alignment = url_alignment
                else:
                    ws.cell(row=row, column=col, value=value)
            else:
                ws.cell(row=row, column=col, value=value)
    excel_file_name = start_date_str + '-' + end_date_str + '-' + biz_zh_name + '-CEP错误整理'
    encoded_file_name = excel_file_name.encode('utf-8')

    # 删除编码转换，直接使用字符串
    if platform.system() == "Windows":
        # Windows直接使用unicode文件名
        wb.save(excel_file_name + ".xlsx")
    elif platform.system() == "Darwin":
        # macOS也直接使用unicode文件名
        wb.save(excel_file_name + ".xlsx")
    else:
        # 其他系统处理
        wb.save(excel_file_name + ".xlsx")


def get_utc_time(y, m, d, h, mm, s):
    dt = datetime.datetime(y, m, d, h, mm, s)
    timezone_offset = datetime.timedelta(hours=0)
    utc_dt = dt - timezone_offset
    return utc_dt.strftime('%Y-%m-%dT%H:%M:%S.000Z')


def query_es_reason(stamp, st, et, trace_id, tenant_id, cloud_config):
    click_visual_url = cloud_config.get("click_visual_url_app_log")
    if (click_visual_url is None):
        print(cloud_config.get("name") + "未配置click_visual_url_app_log")
        return "", None
    tid = cloud_config["app_log_tid"]
    if not cloud_config["click_visual_enabled"]:
        return "", None

    app_name_sql = ""
    if service_name != "" and use_service_name_append_sql:
        app_name_sql = '%27%20and%20app%20%3D%20%27' + service_name

    url = click_visual_url + '?st=' + str(st) + '&et=' + str(
        et) + '&query=traceId%20%3D%20%27' + trace_id + app_name_sql + '%27%20and%20level%20%3D%20%27ERROR' + '%27&pageSize=500&page=1&isQueryCount=0'

    parsed_url = urlparse(url)

    hostname = parsed_url.hostname
    scheme = parsed_url.scheme

    headers = {
        'authority': hostname,
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'referer': scheme + "://" + hostname + '/query?end=' + str(
            et) + '&index=-1&kw=traceId%20%3D%20%27' + trace_id + app_name_sql + '%27&logState=0&page=1&queryType=rawLog&size=300&start=' + str(
            st) + '&tab=custom&tid=' + str(tid),
        'sec-ch-ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': 'macOS',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    request = urllib.request.Request(url, headers=headers)
    try:
        response = urllib.request.urlopen(request)
        data = response.read()
        json_data = json.loads(data.decode('utf-8'))
        datas = json_data["data"]
        if datas is not None:
            print(stamp + "," + tenant_id + ",-------trace_id -------->" + trace_id)
            logs = datas["logs"]
            for log in logs:
                msg = log["msg"]
                error_reason = get_error_reason(trace_id, msg)
                if error_reason != "":
                    return error_reason, datas
    except urllib.error.HTTPError as e:
        print('HTTP Error:', e.code, e.reason)
    except urllib.error.URLError as e:
        print('URL Error:', e.reason)
    # 没有拿到原因时，打印curl命令
    # curl_cmd = f"curl -X GET '{url}' "
    # for k, v in headers.items():
    #     curl_cmd += f"-H '{k}: {v}' "
    # print(f"[query_es_reason] 未拿到原因，curl命令如下：\n{curl_cmd}")
    return "", None


def get_click_visual_url(st, et, trace_id, cloud_config):
    if not cloud_config["click_visual_enabled"]:
        return cloud_config.get("name") + "未开通clickhouse"

    click_visual_url = cloud_config.get("click_visual_url_app_log")
    if (click_visual_url is None):
        return cloud_config.get("name") + "未开通clickhouse"

    tid = cloud_config["app_log_tid"]

    parsed_url = urlparse(click_visual_url)

    hostname = parsed_url.hostname
    scheme = parsed_url.scheme

    return scheme + '://' + hostname + '/query?end=' + str(
        et) + '&index=-1&kw=%20%20traceId%20%3D%20%27' + trace_id + '%27&logState=NaN&page=1&queryType=rawLog&size=100&start=' + str(
        st) + '&tab=custom&tid=' + str(tid)


def get_click_visual_url_error(timestamp, trace_id, tenant_id, cloud_config):
    dt = datetime.fromtimestamp(timestamp / 1000.0)
    two_minutes_before = dt - timedelta(minutes=1)
    two_minutes_after = dt + timedelta(minutes=1)
    st = int(time.mktime(two_minutes_before.timetuple()))
    et = int(time.mktime(two_minutes_after.timetuple()))

    click_visual_url = cloud_config["click_visual_url_app_log"]
    tid = cloud_config["app_log_tid"]

    if not cloud_config["click_visual_enabled"]:
        return "未开通clickhouse"

    parsed_url = urlparse(click_visual_url)

    hostname = parsed_url.hostname
    scheme = parsed_url.scheme

    return scheme + '://' + hostname + '/query?end=' + str(
        et) + '&index=-1&kw=%20%20traceId%20%3D%20%27' + trace_id + '%27&logState=NaN&page=1&queryType=rawLog&size=100&start=' + str(
        st) + '&tab=custom&tid=' + tid


def get_service_command(trace_id):
    return "log traceId " + trace_id


def extract_url_from_curl(msg):
    match = re.search(r"request: curl -X (POST|GET|PUT|DELETE) '([^']+)'", msg)
    if match:
        return match.group(2)
    return None


def get_error_reason(trace_id, msg):
    # 1. methodReportFail:true相关异常优先处理
    if "methodReportFail:true" in msg:
        # 504异常
        if "httpCode:504" in msg:
            url = extract_url_from_curl(msg)
            if url:
                return f"调用接口504异常，接口地址: {url}"
            else:
                return "调用接口504异常，接口地址未知"
        # Connection reset
        if "java.net.SocketException: Connection reset" in msg:
            url = extract_url_from_curl(msg)
            if url:
                return f"调用接口发生Connection reset异常，接口地址: {url}"
            else:
                return "调用接口发生Connection reset异常，接口地址未知"
        # SocketTimeout
        if "java.net.SocketTimeoutException: Read timed out" in msg:
            pattern = r"time:(\d+) ms.*?'(http://[^']*)'"
            match = re.search(pattern, msg)
            if match:
                time_ms = match.group(1)
                url = match.group(2)
                desc = None
                if "/action/Edit" in msg:
                    desc = "调用对象侧Edit接口"
                if "/fs-paas-workflow/paas/crm/approval/instance/afterAction" in msg:
                    desc = "实例后动作重试或忽略"
                if "/udobj-rest4realtime/API/v1/inner/rest/object_describe/" in msg:
                    desc = "调用rest4realtime查询描述"
                if desc is not None:
                    return f"请求{desc}超时,耗时:{time_ms}ms"
                else:
                    return f"请求服务超时,耗时:{time_ms}ms,地址为:{url}"
            else:
                return "请求服务超时,请排查原因"
        # httpCode:200但超时
        if "httpCode:200" in msg:
            match_time = re.search(r"time:(\d+) ms", msg)
            match_timeout = re.search(r"socketTimeOutConfig:(\d+) ms", msg)
            if match_time and match_timeout:
                time_ms = int(match_time.group(1))
                timeout_ms = int(match_timeout.group(1))
                if time_ms > timeout_ms:
                    url = extract_url_from_curl(msg)
                    if url:
                        return f"调用接口超时，接口地址: {url}，耗时: {time_ms} ms，超时阈值: {timeout_ms} ms"
                    else:
                        return f"调用接口超时，接口地址未知，耗时: {time_ms} ms，超时阈值: {timeout_ms} ms"
    # 2. 其他特征异常
    if "/queryBySearchTemplate" in msg and 'httpCode:504' in msg and '元数据调用异常' in msg:
        return "调用queryBySearchTemplate出现504"
    if (
        "/API/v1/inner/rest/object_describe/" in msg or "/v1/rest/object/describe/service/findDescribeExtra" in msg) and 'httpCode:504' in msg and '元数据调用异常' in msg:
        return "查询对象侧描述504异常导致"
    if "java.lang.NullPointerException" in msg:
        return "出现NPE,需要关注"
    if "Get connection error!" in msg:
        return "CEP请求出现Get connection error!需要排查跟进"
    if "/fs-feeds-biz/FeedAdapter/uuid2feedId" in msg and 'httpCode:500' in msg:
        return "调用feed侧接口uuid2feedId异常导致"
    if 'fs-feeds-biz/FeedAdapter/fetchReplyId' in msg and 'httpCode:500' in msg:
        return "调用feed侧接口fetchReplyId异常导致"
    if "com.microsoft.sqlserver.jdbc.SQLServerException" in msg and "com.facishare.feeds.provider.service.FeedsServiceImpl" in msg:
        return "feed测调用sqlserver异常导致"
    if "unknown.QUERY()" in msg:
        return "CEP接口不存在,测试APP的请求到了线上"
    if "Error querying database.  Cause: java.sql.SQLTransientConnectionException: " in msg and 'Connection is not available' in msg:
        pattern = r"Cause: .*? ([a-zA-Z0-9]+) -"
        match = re.search(pattern, msg)
        if match:
            db_name = match.group(1)
            return "连接数据库" + db_name + "异常导致"
    if "nested exception is java.sql.SQLTransientConnectionException" in msg and 'Connection is not available' in msg:
        pattern = r"Cause: .*? ([a-zA-Z0-9]+) -"
        match = re.search(pattern, msg)
        if match:
            db_name = match.group(1)
            return "连接数据库" + db_name + "异常导致"
    if "com.mongodb.MongoSecurityException: Exception authenticating MongoCredential" in msg:
        return msg
    if "Cause: com.alibaba.druid.pool.DataSourceNotAvailableException: org.postgresql.util.PSQLException: ERROR: " \
         "pgbouncer cannot connect to server" in msg:
        pattern = r"SQL: (.*?)\n"
        match = re.search(pattern, msg)
        if match:
            first_sql = match.group(1)
            return "执行sql时出现异常,异常信息:pgbouncer cannot connect to server,SQL:" + first_sql
    # 3. 通用对象侧查询超时
    query_object_address_pattern = r"/API/v1/inner/rest/internal/.*?/(?:\?.*)?"
    query_object_address_pattern2 = r"/API/v1/inner/rest/object_data/.*?/(?:\?.*)?"
    if re.search(query_object_address_pattern, msg) or re.search(query_object_address_pattern2, msg):
        return "调用对象侧查询数据超时"
    # 4. 未识别异常
    return ""


def get_search_ch_error_logs(cloud_config, int_start_date, int_end_date):
    try:
        click_visual_url_log_cep_dist = cloud_config.get("click_visual_url_log_cep_dist")
        if (click_visual_url_log_cep_dist is None):
            print(cloud_config["name"] + "不存在click_visual_url_log_cep_dist")
            return None
        url = click_visual_url_log_cep_dist + "?st=" + str(int_start_date) + "&et=" + str(
            int_end_date) + "&query=biz+=+%27" + biz_name + "%27+and+unknownError+=+true&pageSize=500&page=1&isQueryCount=0"
        headers = {
            'content-type': "application/json",
            'x-fs-userinfo': "-10000",
            'cookie': cookie
        }
        response = requests.request("GET", url, headers=headers)
        try:
            return json.loads(response.text)["data"]["logs"]
        except Exception as e:
            print(f"get_search_ch_error_logs break,ignore:{repr(e)}\nurl: {url}\nresponse: {response.text}")
            return None
    except Exception as e:
        print(f"get_search_ch_error_logs break,ignore:{repr(e)}")
    return None


def get_query_es_date(year, current_month, current_day):
    str_current_month = str(current_month)
    if len(str_current_month) == 1:
        str_current_month = "0" + str_current_month

    str_current_day = str(current_day)
    if len(str_current_day) == 1:
        str_current_day = "0" + str_current_day

    date_format = "%m%d"
    # 当前月+当前日  如 0822
    date_str = str_current_month + str_current_day
    # 通过格式化获取当前日期对象
    # 处理闰年不好用
    # current_date_obj = datetime.strptime(date_str, date_format)

    # 为了闰年2月29日使用
    current_year = dt.now().year  # 获取当前年份
    date_str_with_year = f"{current_year}{date_str}"  # 将当前年份与日期字符串组合
    current_date_obj = dt.strptime(date_str_with_year, '%Y%m%d')

    # 当前日期向前-一天
    before_date = current_date_obj - timedelta(days=1)
    str_before_day = str(before_date.day)
    str_before_month = str(before_date.month)

    if len(str_before_day) == 1:
        str_before_day = "0" + str_before_day

    if len(str_before_month) == 1:
        str_before_month = "0" + str_before_month

    return str(year) + '-' + str_before_month + '-' + str_before_day, str(
        year) + '-' + str_current_month + '-' + str_current_day


if __name__ == '__main__':

    start_date = dt.strptime(start_date_str, '%Y-%m-%d')
    end_date = dt.strptime(end_date_str, '%Y-%m-%d')
    if start_date == end_date:
        # 计算后一天的日期
        end_date = end_date + relativedelta(days=1)

    year, month, day = start_date.year, start_date.month, start_date.day
    print((year, month, day))

    int_start_date = int(time.mktime(start_date.timetuple()))
    int_end_date = int(time.mktime(end_date.timetuple()))

    year_month_day = start_date.strftime("%Y-%m-%d")

    for item in tenant_config:
        cloud_config = tenant_config[item]

        if not cloud_config:
            print("未开通clickhouse")

        # if item != "foneshare.json":
        #     continue

        # 查询出当日所有异常,仅要 500 traceId
        error_data = get_search_ch_error_logs(cloud_config, int_start_date, int_end_date)
        if error_data is not None:
            for error_item in error_data:
                trace_id = error_item["traceId"]
                ei = error_item["ei"]
                if int(ei) in skip_tenant_ids:
                    continue

                cloud_config = get_cloud_config(ei)
                uri = error_item["uri"]
                stamp = error_item["stamp"]

                date_obj = parse(stamp)
                timestamp = time.mktime(date_obj.timetuple())

                dt_obj = dt.fromtimestamp(timestamp)
                two_minutes_before = dt_obj - timedelta(minutes=1)
                two_minutes_after = dt_obj + timedelta(minutes=1)
                st = int(time.mktime(two_minutes_before.timetuple()))
                et = int(time.mktime(two_minutes_after.timetuple()))

                click_visural_url = get_click_visual_url(st, et, trace_id, cloud_config)
                reason, json_data_logs = query_es_reason(stamp, st, et, trace_id, ei, cloud_config)

                simple_logs = ''
                if cloud_config["name"] != '纷享云':
                    if json_data_logs is not None:
                        try:
                            simple_logs = json.dumps(json_data_logs)
                        except Exception as e:
                            print(e)

                item = [stamp, trace_id, uri, click_visural_url,
                        reason, cloud_config["name"], simple_logs]
                witer_excel_file_data_backup.append(item)
    start_date += timedelta(days=1)

export_excel(witer_excel_file_data_backup)