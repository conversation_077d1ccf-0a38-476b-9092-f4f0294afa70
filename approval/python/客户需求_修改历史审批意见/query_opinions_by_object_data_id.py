# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import pymongo
import sys
import argparse
import time

reload(sys)
sys.setdefaultencoding("utf-8")

# 1. mongodb中的opinions,replies
# 2. pg中的biz_flow_task_operate_log的opinion
# 3. pg中approval_task 中的opinions字段

# 添加根目录到路径，以便导入 engine_route_manager
import os
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(root_dir)

from engine_route_manager import MongoEngineManager

config = {
    "foneshare": {
        "apibus_url": "http://172.17.4.230:30393/API/v1",
    },
    "ceshi112": {
        "apibus_url": "http://10.112.5.251:16903/API/v1",
    }
}

# 默认配置（可通过命令行参数覆盖）
DEFAULT_CONFIG = "foneshare"
DEFAULT_TENANT_ID = "762419"
DEFAULT_OBJECT_DATA_IDS = []
DEFAULT_TASK_NAME = ''
DEFAULT_USER_ID = ''

# current_config = "ceshi112"
# tenant_id = "71557"
# object_data_ids = ["6882fbb0fa6a8900074f1ec4"]


users = {
    "2571": "胡晶",
    "1799": "饶美佳",
    "1045": "苏洋",
    "1793": "李慰欣",
    "2501": "林小红",
    "1788": "周舒婷",
    "1077": "陈洁群",
    "2871": "何珊珊",
    "1068": "刘仕博",
    "1298": "胡园园",
    "1795": "王娉婷",
    "1157": "叶思琪",
    "1464": "全承平",
    "3083": "李晓冰",
    "1038": "徐荣强",
    "1791": "张玲容",
    "1385": "胡东立",
    "2038": "袁婷婷",
    "2297": "梁海燕",
    "1774": "罗艳君"

}


def get_format_date(create_time):
    # 转换成localtime
    time_local = time.localtime(create_time / 1000)
    return time.strftime("%d/%m/%Y %H:%M", time_local)  # 11/24/23 22:35


def get_user_id_by_name(user_name):
    """
    通过用户名获取用户ID
    """
    if not user_name:
        return None

    # 创建反向映射：用户名 -> 用户ID
    name_to_id = {}
    for user_id, name in users.items():
        name_to_id[name] = user_id

    return name_to_id.get(user_name)


def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(
        description='查询审批意见脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  # 基本用法
  python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577

  # 指定多个object_data_id
  python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577,6882fbb0fa6a8900074f1ec4

  # 指定任务名称和用户ID
  python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577 --task-name "审批任务" --user-id 2571

  # 通过用户名查询
  python query_opinions_by_object_data_id.py --object-data-ids 6560b356a5323f0001d63577 --user-id 胡晶

  # 指定配置和租户ID
  python query_opinions_by_object_data_id.py --config ceshi112 --tenant-id 71557 --object-data-ids 6882fbb0fa6a8900074f1ec4
        '''
    )

    parser.add_argument(
        '--config', '-c',
        default=DEFAULT_CONFIG,
        choices=['foneshare', 'ceshi112'],
        help='配置环境 (默认: {0})'.format(DEFAULT_CONFIG)
    )

    parser.add_argument(
        '--tenant-id', '-t',
        default=DEFAULT_TENANT_ID,
        help='租户ID (默认: {0})'.format(DEFAULT_TENANT_ID)
    )

    parser.add_argument(
        '--object-data-ids', '-o',
        required=True,
        help='对象数据ID列表，多个ID用逗号分隔 (必需参数)'
    )

    parser.add_argument(
        '--task-name', '-n',
        default=DEFAULT_TASK_NAME,
        help='任务名称过滤条件 (默认: 空，不过滤)'
    )

    parser.add_argument(
        '--user-id', '-u',
        default=DEFAULT_USER_ID,
        help='用户ID或用户名过滤条件，支持用户ID(如:2571)或用户名(如:胡晶) (默认: 空，不过滤)'
    )

    return parser.parse_args()


if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()

    # 从参数中获取配置
    current_config = args.config
    tenant_id = args.tenant_id
    object_data_ids = [id.strip() for id in args.object_data_ids.split(',') if id.strip()]
    task_name = args.task_name
    user_input = args.user_id

    # 处理用户ID：如果输入的是用户名，转换为用户ID
    user_id = user_input
    if user_input:
        # 先尝试通过用户名查找用户ID
        found_user_id = get_user_id_by_name(user_input)
        if found_user_id:
            user_id = found_user_id
            print("检测到用户名 '{0}'，已转换为用户ID: {1}".format(user_input, user_id))
        else:
            # 如果没找到对应的用户名，假设输入的就是用户ID
            user_id = user_input
            if user_input in users:
                print("使用用户ID: {0} ({1})".format(user_id, users[user_id]))
            else:
                print("警告: 用户ID '{0}' 在用户映射表中未找到".format(user_id))

    print("配置信息:")
    print("  环境: {0}".format(current_config))
    print("  租户ID: {0}".format(tenant_id))
    print("  对象数据ID: {0}".format(object_data_ids))
    print("  任务名称过滤: {0}".format(task_name if task_name else "无"))
    if user_id:
        user_name = users.get(user_id, "未知用户")
        print("  用户过滤: {0} ({1})".format(user_id, user_name))
    else:
        print("  用户过滤: 无")
    print("-" * 50)

    manager = MongoEngineManager(current_config)
    engine_tdb = manager.get_engine_db(tenant_id, read_preference=pymongo.read_preferences.ReadPreference.SECONDARY)

    for object_id in object_data_ids:
        workflowInstances = engine_tdb.workflowInstances.find({
            "tenantId": tenant_id,
            "type": "approvalflow",
            "appId": "CRM",
            "objectId": object_id,
        })

        for workflowInstance in workflowInstances:
            _workflow_instance_id = workflowInstance.get("_id")
            workflow_instance_id = str(_workflow_instance_id)

            tasks = engine_tdb.tasks.find({
                "tenantId": tenant_id,
                "type": "approvalflow",
                "appId": "CRM",
                "objectId": object_id,
                "workflowInstanceId": workflow_instance_id
            }).batch_size(100)

            for task in tasks:
                _task_id = task.get("_id")
                task_id = str(_task_id)
                name = task.get("name")
                opinions = task.get("opinions")
                replies = task.get("replies")

                # 任务名称过滤：如果指定了task_name，则只处理匹配的任务；否则处理所有任务
                if (not task_name or name == task_name) and opinions is not None:

                    for opinion in opinions:
                        opinion_id = opinion.get("id")
                        feed_id = opinion.get("feedId")
                        reply_id = opinion.get("replyId")
                        opinion_content = opinion.get("opinion")
                        reply_time = opinion.get("replyTime")
                        action_type = opinion.get("actionType")
                        opinion_user_id = opinion.get("userId")

                        # 用户ID过滤：如果指定了user_id，则只处理匹配的用户；否则处理所有用户
                        if not user_id or opinion_user_id == user_id:
                            user_name = users.get(opinion_user_id, "未知用户")
                            print (
                                    "意见:objectId:{0},taskId:{1},节点名称:{2},opinionId:{3},原意见:{4},审批时间:{5},审批状态:{6},需要修改为:业务区跟进回款闭环,feedId:{7},replyId:{8},userId:{9},userName:{10}".format(
                                object_id,
                                task_id,
                                name,
                                opinion_id,
                                opinion_content,
                                get_format_date(reply_time) if reply_time else "无",
                                action_type,
                                feed_id,
                                reply_id,
                                opinion_user_id,
                                user_name))
