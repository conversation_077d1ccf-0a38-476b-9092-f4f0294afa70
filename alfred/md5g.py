# -*- coding: utf-8 -*-
__author__ = 'cuiyx'

import json
import sys
import datetime
from os import listdir
import os
import pathlib
# from pathlib import Path
from os.path import isdir, join, expanduser
import md5

from workflow import Workflow3

reload(sys)  # Python2.5 初始化后会删除 sys.setdefaultencoding 这个方法，我们需要重新载入
sys.setdefaultencoding('utf-8')

wf = Workflow3()
log = wf.logger


def gen_md5(wf):
    md5_content = wf.args[0]
    log.debug('md5 content: ' + md5_content)
    wf.add_item(title=file_name, arg=x, valid=True)
    wf.send_feedback()


if __name__ == '__main__':
    sys.exit(wf.run(gen_md5))
